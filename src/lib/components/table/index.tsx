import { View } from '@react-pdf/renderer';
import React from 'react';

interface TableProps extends React.ComponentProps<typeof View> {}
export const Table: React.FC<TableProps> = (props) => {
    return (
        <View
            {...props}
            style={{ borderStyle: 'solid', borderColor: 'black', borderLeft: '1px', borderTop: '1px', ...props.style }}
        >
            {props.children}
        </View>
    );
};

interface TableRowProps extends React.ComponentProps<typeof View> {}
export const TableRow: React.FC<TableRowProps> = (props) => {
    return (
        <View {...props} style={{ display: 'flex', flexDirection: 'row', ...props.style }}>
            {props.children}
        </View>
    );
};

interface TableCellProps extends React.ComponentProps<typeof View> {
    dense?: boolean;
}
export const TableCell: React.FC<TableCellProps> = ({ dense = false, ...props }) => {
    return (
        <View
            {...props}
            style={{
                padding: dense ? '4px 4px' : '8px 8px',
                justifyContent: 'center',
                borderStyle: 'solid',
                borderColor: 'black',
                borderRight: '1px',
                borderBottom: '1px',
                ...props.style,
            }}
        >
            {props.children}
        </View>
    );
};
