import { renderToString, renderToFile, renderToStream } from '@react-pdf/renderer';
import React from 'react';

export class PDFHelper<T> {
    component: React.FunctionComponentElement<any>;
    constructor(component: React.FC<any>, props: any) {
        this.component = React.createElement(component, props);
    }
    toBase64() {
        return renderToString(this.component);
    }
    toFile(filePath: string) {
        return renderToFile(this.component, filePath);
    }
    toStream() {
        return renderToStream(this.component);
    }
}
