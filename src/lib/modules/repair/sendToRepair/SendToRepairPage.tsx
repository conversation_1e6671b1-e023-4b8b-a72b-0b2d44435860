import React from 'react';
import numeral from 'numeral';

import { Image, Page, StyleSheet, Text, View, Font } from '@react-pdf/renderer';
import { TRADITIONAL_CHINESE } from '../../../consts';
import { Table, TableRow, TableCell } from '../../../components/table';
import { EnumLanguage } from '../../../consts';
import SendToRepairItemTable from './SendToRepairItem';
import ISOFooter from '../../common/ISOFooter';
import CheckBox from '../../common/CheckBox';
import path from 'path';
import { SendToRepairDocData } from './types';

Font.register({
    family: TRADITIONAL_CHINESE,
    src: path.join(__dirname, '../../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
});
Font.registerHyphenationCallback((word) => word.split(''));

// Create styles
const styles = StyleSheet.create({
    page: {
        fontFamily: TRADITIONAL_CHINESE,
        fontSize: 8,
        padding: '8mm 10mm',
    },
    row: {
        display: 'flex',
        flexDirection: 'row',
    },
});
const docInfo = {
    date: '2023.1.1',
    version: '6',
    fileCode: 'SC-N-4-7-161',
};
interface Props {
    fileTitle?: string;
    orderCodeBarcode?: string;
    memberCodeBarcode?: string;
    clientWebQRCode?: string;
    signature?: string;
    data?: SendToRepairDocData;
    itemOffset: number;
    hiddenFooter?: boolean;
    lang: EnumLanguage;
}
const SendToRepairPage: React.FC<Props> = (props) => {
    const {
        fileTitle,
        orderCodeBarcode,
        memberCodeBarcode,
        clientWebQRCode,
        signature,
        data,
        itemOffset,
        hiddenFooter = false,
        lang,
    } = props;

    return (
        <Page size="A4" style={styles.page}>
            <Text
                style={{ textAlign: 'right' }}
                render={({ pageNumber, totalPages }) => `(${pageNumber} / ${totalPages})`}
                fixed
            />
            <View style={{ ...styles.row, marginBottom: '2mm' }}>
                <View style={{ flex: 1, alignItems: 'center', ...styles.row }}>
                    <Text style={{ marginRight: 8 }}>單號</Text>
                    {orderCodeBarcode && <Image src={orderCodeBarcode} style={{ height: '10mm' }} />}
                </View>
                <View style={{ flex: 1, textAlign: 'center' }}>
                    <Text style={{ fontSize: '11pt', lineHeight: '1.5' }}>{fileTitle}</Text>
                    <Text>維修訂單(送修聯)</Text>
                </View>
                <View style={{ flex: 1, alignItems: 'center', ...styles.row, justifyContent: 'flex-end' }}>
                    <Text style={{ marginRight: 8 }}>客編</Text>
                    {memberCodeBarcode && <Image src={memberCodeBarcode} style={{ height: '10mm' }} />}
                </View>
            </View>
            <Table>
                <TableRow>
                    <TableCell style={{ width: '13.3%' }}>
                        <Text>單號</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%', paddingTop: 0, paddingBottom: 0 }}>
                        <Text>{data?.orderCode}</Text>
                    </TableCell>
                    <TableCell style={{ width: '13.3%' }}>
                        <Text>收件單位</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%', paddingTop: 0, paddingBottom: 0 }}>
                        <Text>{data ? `${data.receivedStoreCode} ${data.receivedStoreName}` : ''}</Text>
                    </TableCell>
                    <TableCell style={{ width: '13.4%' }}>
                        <Text>收件人員</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>
                        <Text>{data ? `${data.receivedUserCode} ${data.receivedUserName}` : ''}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '13.3%' }}>
                        <Text>客戶姓名</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>
                        <Text>{data?.memberName}</Text>
                    </TableCell>
                    <TableCell style={{ width: '13.3%' }}>
                        <Text>聯絡電話</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>
                        <Text>{data?.contactPhone}</Text>
                    </TableCell>
                    <TableCell style={{ width: '13.4%' }}>
                        <Text>收件日期</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>
                        <Text>{data?.receivedDate}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '13.3%' }}>
                        <Text>客戶編號</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>
                        <Text>{data?.memberCode}</Text>
                    </TableCell>
                    <TableCell style={{ width: '13.3%' }}>
                        <Text>行動電話</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>
                        <Text>{data?.cellphone}</Text>
                    </TableCell>
                    <TableCell style={{ width: '13.4%' }}>
                        <Text>交貨方式</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>
                        <Text>{data?.deliveryMethod}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '13.3%' }}>
                        <Text>交貨地址</Text>
                    </TableCell>
                    <TableCell style={{ width: '86.7%' }}>
                        <Text>{data?.deliveryAddress}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '13.3%', borderRight: 0 }}>
                        <Text>檢測費(A)</Text>
                    </TableCell>
                    <TableCell style={{ width: '36.7%' }}>
                        <Text>{data ? numeral(data.testFee).format('0,0') : ''}</Text>
                    </TableCell>
                    <TableCell style={{ width: '13.3%', borderRight: 0 }}>
                        <Text>品項總數</Text>
                    </TableCell>
                    <TableCell style={{ width: '36.7%' }}>
                        <Text>{data?.qty}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '13.3%', borderRight: 0 }}>
                        <Text>隨附配件</Text>
                    </TableCell>
                    <TableCell style={{ width: '86.7%' }}>
                        <Text>{data?.accessory}</Text>
                    </TableCell>
                </TableRow>
                <SendToRepairItemTable item={data?.items[itemOffset]} lang={lang} />
                <SendToRepairItemTable item={data?.items[itemOffset + 1]} lang={lang} />
                <TableRow>
                    <TableCell style={{ width: '14%', justifyContent: 'flex-start' }}>
                        <Text>外觀檢查</Text>
                    </TableCell>
                    <TableCell style={{ width: '43%', justifyContent: 'flex-start' }}>
                        <View style={{ display: 'flex', flexDirection: 'row', marginBottom: 4 }}>
                            <CheckBox
                                style={{ marginRight: 4 }}
                                check={data?.checkingExterior === 'Normal'}
                                label="正常"
                            />
                            <CheckBox
                                style={{ marginRight: 4 }}
                                check={data?.checkingExterior === 'Worn'}
                                label="損毀(包含產品本體、外殼、配件)"
                            />
                        </View>
                        <Text>{`備註: ${data ? data.checkingExteriorRemark : ''}`}</Text>
                    </TableCell>
                    <TableCell style={{ width: '43%', padding: 0, border: '2px solid black' }}>
                        <Text style={{ padding: '2px 4px 2px', borderBottom: '1px solid black' }}>
                            本人茲確認後簽名，商品外觀如上所載
                        </Text>
                        <View style={{ padding: '4px 4px' }}>
                            <View style={{ ...styles.row }}>
                                <Text>客戶簽名</Text>
                                <View style={{ height: '10mm', padding: '0 2px 2px' }}>
                                    {signature && <Image src={signature} style={{ height: '100%' }} />}
                                </View>
                            </View>
                            <Text style={{ fontSize: '7pt' }}>{data?.extendedWarranty}</Text>
                        </View>
                    </TableCell>
                </TableRow>
            </Table>
            <View style={{ margin: '8px 0' }}>
                <ISOFooter date={docInfo.date} version={docInfo.version} fileCode={docInfo.fileCode} lang={lang} />
            </View>
            <View style={{ borderTop: '1px solid black', padding: '4px 0' }} />
            <View style={{ textAlign: 'center' }}>
                <Text>客戶取貨收執聯(請門市蓋店章)</Text>
            </View>
            <Table>
                <TableRow>
                    <TableCell style={{ width: '10%', borderRight: 0, justifyContent: 'flex-start' }}>
                        <Text>注意事項</Text>
                    </TableCell>
                    <TableCell style={{ width: '70%', borderRight: 0 }}>
                        <Text>
                            1.該產品經由工程師檢測，如發現產品故障屬於非保固範圍之原因，例如:人為損壞、液體入侵、受潮、天災、蟲害......等因素導致的故障，或交由非原廠授權代理商服務所造成的損壞，該情形需由用戶支付維修費用。
                        </Text>
                        <Text>2.維修報價於三天內回覆是否維修，以減少您等候的時間。</Text>
                        <Text>3.機器本體經維修或經交換新品後的不良品及不良零件所有權歸本公司所有。</Text>
                        <Text>
                            4.本公司客戶送修品保固期外，由收件單位與客戶酌收檢測費300元，經報價不修亦不退檢測費經報價後客戶確定支付費用維修，此檢測費可抵扣維修費。非本公司客戶收費標準，另行公告。
                        </Text>
                        <Text>
                            5.維修完成件(含報價)通知日起算，請儘速取件，逾 60
                            日內未領取，本公司恕不負保管責任，不再另作通知。
                        </Text>
                        <Text>6.以上資料本公司係作為售後服務目的之建檔、聯繫與客戶滿意度調查使用。</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>{clientWebQRCode && <Image src={clientWebQRCode} />}</TableCell>
                </TableRow>
            </Table>
            <Table style={{ borderWidth: 2 }}>
                <TableRow>
                    <TableCell style={{ width: '18%' }}>
                        <Text>單號</Text>
                    </TableCell>
                    <TableCell style={{ width: '32%' }}>
                        <Text>{data?.orderCode}</Text>
                    </TableCell>
                    <TableCell style={{ width: '18%' }}>
                        <Text>收件人員及日期</Text>
                    </TableCell>
                    <TableCell style={{ width: '16%' }}>
                        <Text>{data?.receivedUserName}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16%' }}>
                        <Text>{data?.receivedDate}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '18%' }}>
                        <Text>茲證明收到之客戶</Text>
                    </TableCell>
                    <TableCell style={{ width: '32%' }}>
                        <Text>{data?.memberName}</Text>
                    </TableCell>
                    <TableCell style={{ width: '18%' }}>
                        <Text>隨附配件</Text>
                    </TableCell>
                    <TableCell style={{ width: '32%' }}>
                        <Text>{data?.accessory}</Text>
                    </TableCell>
                </TableRow>
                <TableRow style={{ height: '12mm' }}>
                    <TableCell style={{ width: '18%' }}>
                        <Text>1:產品型號</Text>
                    </TableCell>
                    <TableCell style={{ width: '32%', paddingTop: 0, paddingBottom: 0 }}>
                        <Text>{data?.items[itemOffset]?.materialName}</Text>
                    </TableCell>
                    <TableCell style={{ width: '18%' }}>
                        <Text>2:產品型號</Text>
                    </TableCell>
                    <TableCell style={{ width: '32%', paddingTop: 0, paddingBottom: 0 }}>
                        <Text>{data?.items[itemOffset + 1]?.materialName}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '18%' }}>
                        <Text>1:產品序號</Text>
                    </TableCell>
                    <TableCell style={{ width: '32%' }}>
                        <Text>{data?.items[itemOffset]?.SN}</Text>
                    </TableCell>
                    <TableCell style={{ width: '18%' }}>
                        <Text>2:產品序號</Text>
                    </TableCell>
                    <TableCell style={{ width: '32%' }}>
                        <Text>{data?.items[itemOffset + 1]?.SN}</Text>
                    </TableCell>
                </TableRow>
            </Table>
            {!hiddenFooter && (
                <View style={{ margin: '8px 0' }}>
                    <ISOFooter date={docInfo.date} version={docInfo.version} fileCode={docInfo.fileCode} lang={lang} />
                </View>
            )}
        </Page>
    );
};
export default SendToRepairPage;
