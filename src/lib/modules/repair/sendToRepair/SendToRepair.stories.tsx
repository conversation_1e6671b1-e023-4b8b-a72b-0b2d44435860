import React, { useEffect, useState } from 'react';
import { Font, PDFViewer } from '@react-pdf/renderer';
import QRCode from 'qrcode';

import { ComponentStory, ComponentMeta } from '@storybook/react';

import { base64Barcode } from '../../../../utils/barcode';
import { SendToRepairMockData } from '../../../../mocks/sendToRepair';
import SendToRepairDocument from './SendToRepairDocument';

const SendToRepairStory: ComponentMeta<typeof SendToRepairDocument> = {
    title: 'Repair/SendToRepair',
    component: SendToRepairDocument,
};
export default SendToRepairStory;

const Template: ComponentStory<typeof SendToRepairDocument> = (args) => {
    const { data } = args;
    const [orderCodeBarcode, setOrderCodeBarcode] = useState<string>();
    useEffect(() => {
        (async () => {
            data?.orderCode && setOrderCodeBarcode(await base64Barcode(data.orderCode));
        })();
    }, [data?.orderCode]);
    const [memberCodeBarode, setMemberCodeBarcode] = useState<string>();
    useEffect(() => {
        (async () => {
            data?.memberCode && setMemberCodeBarcode(await base64Barcode(data.memberCode));
        })();
    }, [data?.memberCode]);

    const [urlQRCode, setUrlQRCode] = useState<string>();
    useEffect(() => {
        (async () => {
            data &&
                setUrlQRCode(
                    await QRCode.toDataURL(`http://tw.store.dev.clinico.cloud/clinico/repair/detail?id=28045`)
                );
        })();
    }, [data]);

    return (
        <PDFViewer width="100%" height="800">
            <SendToRepairDocument
                {...args}
                orderCodeBarcode={orderCodeBarcode}
                memberCodeBarcode={memberCodeBarode}
                clientWebQRCode={urlQRCode}
            />
        </PDFViewer>
    );
};
export const Empty = Template.bind({});
Empty.args = {
    fileTitle: '(公司名稱)',
    // data: {},
};
export const Mock = Template.bind({});
Mock.args = {
    fileTitle: '科林儀器股份有限公司',
    data: SendToRepairMockData,
    signature: '/sign.png',
};

export const ThreeItem = Template.bind({});
ThreeItem.args = {
    fileTitle: '科林儀器股份有限公司',
    data: {
        ...SendToRepairMockData,
        items: [SendToRepairMockData.items[0], SendToRepairMockData.items[1], SendToRepairMockData.items[0]],
    },
};
