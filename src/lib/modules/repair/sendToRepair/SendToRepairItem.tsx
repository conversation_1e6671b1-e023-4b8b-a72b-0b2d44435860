import React from 'react';
import { EnumLanguage } from '../../../consts';
import { Text, View } from '@react-pdf/renderer';
import { TableCell, TableRow } from '../../../components/table';
import { SendToRepairItem } from './types';
import CheckBox from '../../common/CheckBox';

interface Props {
    item?: SendToRepairItem;
    lang: EnumLanguage;
}
const SendToRepairItemTable: React.FC<Props> = (props) => {
    const { item, lang } = props;
    return (
        <View>
            <TableRow>
                <TableCell style={{ width: '15%' }}>
                    <View>
                        {lang === EnumLanguage['zh-cn'] && (
                            <>
                                <Text>产品编号</Text>
                            </>
                        )}
                        {lang === EnumLanguage['zh-tw'] && (
                            <>
                                <Text>產品編號</Text>
                            </>
                        )}
                        <View style={{ height: '10mm', justifyContent: 'center' }}>
                            <Text>{item?.materialCode}</Text>
                        </View>
                    </View>
                </TableCell>
                <TableCell style={{ width: '15%' }}>
                    <View>
                        {lang === EnumLanguage['zh-cn'] && (
                            <>
                                <Text>产品型号</Text>
                            </>
                        )}
                        {lang === EnumLanguage['zh-tw'] && (
                            <>
                                <Text>產品型號</Text>
                            </>
                        )}
                        <View style={{ height: '10mm', justifyContent: 'center' }}>
                            <Text>{item?.materialName}</Text>
                        </View>
                    </View>
                </TableCell>
                <TableCell style={{ width: '15%' }}>
                    <View>
                        {lang === EnumLanguage['zh-cn'] && (
                            <>
                                <Text>产品序号</Text>
                            </>
                        )}
                        {lang === EnumLanguage['zh-tw'] && (
                            <>
                                <Text>產品序號</Text>
                            </>
                        )}
                        <View style={{ height: '10mm', justifyContent: 'center' }}>
                            <Text>{item?.SN}</Text>
                        </View>
                    </View>
                </TableCell>
                <TableCell style={{ width: '15%' }}>
                    <View>
                        {lang === EnumLanguage['zh-cn'] && (
                            <>
                                <Text>耳内序号</Text>
                            </>
                        )}
                        {lang === EnumLanguage['zh-tw'] && (
                            <>
                                <Text>耳內序號</Text>
                            </>
                        )}
                        <View style={{ height: '10mm', justifyContent: 'center' }}>
                            <Text>{item?.inEarSN}</Text>
                        </View>
                    </View>
                </TableCell>
                <TableCell style={{ width: '12.5%' }}>
                    <View>
                        {lang === EnumLanguage['zh-cn'] && (
                            <>
                                <Text>购买日期</Text>
                            </>
                        )}
                        {lang === EnumLanguage['zh-tw'] && (
                            <>
                                <Text>購買日期</Text>
                            </>
                        )}
                        <View style={{ height: '10mm', justifyContent: 'center' }}>
                            <Text>{item?.shippingDate}</Text>
                        </View>
                    </View>
                </TableCell>
                <TableCell style={{ width: '12.5%' }}>
                    <View>
                        <Text>保固到期</Text>
                        <View style={{ height: '10mm', justifyContent: 'center' }}>
                            <Text>{item?.warrantyDate}</Text>
                        </View>
                    </View>
                </TableCell>
                <TableCell style={{ width: '15%' }}>
                    <View>
                        <Text>其他</Text>
                        <View style={{ height: '10mm', justifyContent: 'center' }}>
                            <Text>{item?.other}</Text>
                        </View>
                    </View>
                </TableCell>
            </TableRow>
            <TableRow>
                <TableCell style={{ width: '100%' }}>
                    <View>
                        <Text style={{ marginBottom: 4 }}>送修原因</Text>
                        <View style={{ height: '13mm', display: 'flex', flexDirection: 'row', flexWrap: 'wrap' }}>
                            {item?.reasons.map((reason, index) => (
                                <CheckBox
                                    key={index}
                                    style={{ marginRight: 4, marginBottom: 4 }}
                                    check={reason.check}
                                    label={reason.name}
                                />
                            ))}
                        </View>
                    </View>
                </TableCell>
            </TableRow>
        </View>
    );
};
export default SendToRepairItemTable;
