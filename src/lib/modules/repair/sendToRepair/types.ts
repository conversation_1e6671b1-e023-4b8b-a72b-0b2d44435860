export interface SendToRepairDocData {
    orderCode: string;
    receivedStoreCode: string;
    receivedStoreName: string;
    receivedUserCode: string;
    receivedUserName: string;
    memberName: string;
    memberCode: string;
    contactPhone?: string;
    cellphone?: string;
    receivedDate: string;
    deliveryMethod: string;
    deliveryAddress: string;
    testFee: number;
    qty: number;
    accessory?: string;
    checkingExterior: 'Normal' | 'Worn';
    checkingExteriorRemark?: string;
    items: SendToRepairItem[];
    extendedWarranty?: string;
}
export interface SendToRepairItem {
    materialCode: string;
    materialName: string;
    SN?: string;
    inEarSN?: string;
    other?: string;
    shippingDate?: string;
    warrantyDate?: string;
    reasons: SendToRepairItemReason[];
}
export interface SendToRepairItemReason {
    check: boolean;
    name: string;
}
