import React from 'react';
import numeral from 'numeral';

import { Image, Page, StyleSheet, Text, View, Font } from '@react-pdf/renderer';
import { TRADITIONAL_CHINESE } from '../../../consts';
import { Table, TableRow, TableCell } from '../../../components/table';
import { EnumLanguage } from '../../../consts';
import SendToRepairItemTable from './SendToRepairItem';
import ISOFooter from '../../common/ISOFooter';
import CheckBox from '../../common/CheckBox';
import path from 'path';
import { SendToRepairDocData } from './types';

Font.register({
    family: TRADITIONAL_CHINESE,
    src: path.join(__dirname, '../../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
});
Font.registerHyphenationCallback((word) => word.split(''));

// Create styles
const styles = StyleSheet.create({
    page: {
        fontFamily: TRADITIONAL_CHINESE,
        fontSize: 8,
        padding: '8mm 10mm',
    },
    row: {
        display: 'flex',
        flexDirection: 'row',
    },
});
const docInfo = {
    date: '2023.1.1',
    version: '6',
    fileCode: 'SC-N-4-7-161',
};
interface Props {
    fileTitle?: string;
    orderCodeBarcode?: string;
    memberCodeBarcode?: string;
    clientWebQRCode?: string;
    signature?: string;
    data?: SendToRepairDocData;
    itemOffset: number;
    hiddenFooter?: boolean;
    lang: EnumLanguage;
}
const SendToRepairPageCN: React.FC<Props> = (props) => {
    const {
        fileTitle,
        orderCodeBarcode,
        memberCodeBarcode,
        clientWebQRCode,
        signature,
        data,
        itemOffset,
        hiddenFooter = false,
        lang,
    } = props;

    return (
        <Page size="A4" style={styles.page}>
            <Text
                style={{ textAlign: 'right' }}
                render={({ pageNumber, totalPages }) => `(${pageNumber} / ${totalPages})`}
                fixed
            />
            <View style={{ ...styles.row, marginBottom: '2mm' }}>
                <View style={{ flex: 1, alignItems: 'center', ...styles.row }}>
                    <Text style={{ marginRight: 8 }}>单号</Text>
                    {orderCodeBarcode && <Image src={orderCodeBarcode} style={{ height: '10mm' }} />}
                </View>
                <View style={{ flex: 1, textAlign: 'center' }}>
                    <Text style={{ fontSize: '11pt', lineHeight: '1.5' }}>{fileTitle}</Text>
                    <Text>维修订单(送修联)</Text>
                </View>
                <View style={{ flex: 1, alignItems: 'center', ...styles.row, justifyContent: 'flex-end' }}>
                    <Text style={{ marginRight: 8 }}>客编</Text>
                    {memberCodeBarcode && <Image src={memberCodeBarcode} style={{ height: '10mm' }} />}
                </View>
            </View>
            <Table>
                <TableRow>
                    <TableCell style={{ width: '13.3%' }}>
                        <Text>单号</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%', paddingTop: 0, paddingBottom: 0 }}>
                        <Text>{data?.orderCode}</Text>
                    </TableCell>
                    <TableCell style={{ width: '13.3%' }}>
                        <Text>收件单位</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%', paddingTop: 0, paddingBottom: 0 }}>
                        <Text>{data ? `${data.receivedStoreCode} ${data.receivedStoreName}` : ''}</Text>
                    </TableCell>
                    <TableCell style={{ width: '13.4%' }}>
                        <Text>收件人员</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>
                        <Text>{data ? `${data.receivedUserCode} ${data.receivedUserName}` : ''}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '13.3%' }}>
                        <Text>客户姓名</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>
                        <Text>{data?.memberName}</Text>
                    </TableCell>
                    <TableCell style={{ width: '13.3%' }}>
                        <Text>联络电话</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>
                        <Text>{data?.contactPhone}</Text>
                    </TableCell>
                    <TableCell style={{ width: '13.4%' }}>
                        <Text>收件日期</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>
                        <Text>{data?.receivedDate}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '13.3%' }}>
                        <Text>客户编号</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>
                        <Text>{data?.memberCode}</Text>
                    </TableCell>
                    <TableCell style={{ width: '13.3%' }}>
                        <Text>行动电话</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>
                        <Text>{data?.cellphone}</Text>
                    </TableCell>
                    <TableCell style={{ width: '13.4%' }}>
                        <Text>交货方式</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>
                        <Text>{data?.deliveryMethod}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '13.3%' }}>
                        <Text>交货地址</Text>
                    </TableCell>
                    <TableCell style={{ width: '86.7%' }}>
                        <Text>{data?.deliveryAddress}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '13.3%', borderRight: 0 }}>
                        <Text>检测费(A)</Text>
                    </TableCell>
                    <TableCell style={{ width: '36.7%' }}>
                        <Text>{data ? numeral(data.testFee).format('0,0') : ''}</Text>
                    </TableCell>
                    <TableCell style={{ width: '13.3%', borderRight: 0 }}>
                        <Text>品项总数</Text>
                    </TableCell>
                    <TableCell style={{ width: '36.7%' }}>
                        <Text>{data?.qty}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '13.3%', borderRight: 0 }}>
                        <Text>随附配件</Text>
                    </TableCell>
                    <TableCell style={{ width: '86.7%' }}>
                        <Text>{data?.accessory}</Text>
                    </TableCell>
                </TableRow>
                <SendToRepairItemTable item={data?.items[itemOffset]} lang={lang} />
                <SendToRepairItemTable item={data?.items[itemOffset + 1]} lang={lang} />
                <TableRow>
                    <TableCell style={{ width: '14%', justifyContent: 'flex-start' }}>
                        <Text>外观检查</Text>
                    </TableCell>
                    <TableCell style={{ width: '43%', justifyContent: 'flex-start' }}>
                        <View style={{ display: 'flex', flexDirection: 'row', marginBottom: 4 }}>
                            <CheckBox
                                style={{ marginRight: 4 }}
                                check={data?.checkingExterior === 'Normal'}
                                label="正常"
                            />
                            <CheckBox
                                style={{ marginRight: 4 }}
                                check={data?.checkingExterior === 'Worn'}
                                label="损毁(包含产品本体、外壳、配件)"
                            />
                        </View>
                        <Text>{`备注: ${data ? data.checkingExteriorRemark : ''}`}</Text>
                    </TableCell>
                    <TableCell style={{ width: '43%', padding: 0, border: '2px solid black' }}>
                        <Text style={{ padding: '2px 4px 2px', borderBottom: '1px solid black' }}>
                            本人兹确认后签名，商品外观如上所载
                        </Text>
                        <View style={{ padding: '4px 4px' }}>
                            <View style={{ ...styles.row }}>
                                <Text>客户签名</Text>
                                <View style={{ height: '10mm', padding: '0 2px 2px' }}>
                                    {signature && <Image src={signature} style={{ height: '100%' }} />}
                                </View>
                            </View>
                            <Text style={{ fontSize: '7pt' }}>{data?.extendedWarranty}</Text>
                        </View>
                    </TableCell>
                </TableRow>
            </Table>
            <View style={{ margin: '8px 0' }}>
                <ISOFooter date={docInfo.date} version={docInfo.version} fileCode={docInfo.fileCode} lang={lang} />
            </View>
            <View style={{ borderTop: '1px solid black', padding: '4px 0' }} />
            <View style={{ textAlign: 'center' }}>
                <Text>客户取货收执联(请门市盖店章)</Text>
            </View>
            <Table>
                <TableRow>
                    <TableCell style={{ width: '10%', borderRight: 0, justifyContent: 'flex-start' }}>
                        <Text>注意事项</Text>
                    </TableCell>
                    <TableCell style={{ width: '70%', borderRight: 0 }}>
                        <Text>
                            1.该产品经由工程师检测，如发现产品故障属于非保固范围之原因，例如:人为损坏、液体入侵、受潮、天灾、虫害......等因素导致的故障，或交由非原厂授权代理商服务所造成的损坏，该情形需由用户支付维修费用。
                        </Text>
                        <Text>2.维修报价于三天内回覆是否维修，以减少您等候的时间。</Text>
                        <Text>3.机器本体经维修或经交换新品后的不良品及不良零件所有权归本公司所有。</Text>
                        <Text>
                            4.本公司客户送修品保固期外，由收件单位与客户酌收检测费 ________
                            元，经报价不修亦不退检测费经报价后客户确定支付费用维修，此检测费可抵扣维修费。非本公司客户收费标准，另行公告。
                        </Text>
                        <Text>
                            5.维修完成件(含报价)通知日起算，请尽速取件，逾 60
                            日内未领取，本公司恕不负保管责任，不再另作通知。
                        </Text>
                        <Text>6.以上资料本公司系作为售后服务目的之建档、联系与客户满意度调查使用。</Text>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>{clientWebQRCode && <Image src={clientWebQRCode} />}</TableCell>
                </TableRow>
            </Table>
            <Table style={{ borderWidth: 2 }}>
                <TableRow>
                    <TableCell style={{ width: '18%' }}>
                        <Text>单号</Text>
                    </TableCell>
                    <TableCell style={{ width: '32%' }}>
                        <Text>{data?.orderCode}</Text>
                    </TableCell>
                    <TableCell style={{ width: '18%' }}>
                        <Text>收件人员及日期</Text>
                    </TableCell>
                    <TableCell style={{ width: '16%' }}>
                        <Text>{data?.receivedUserName}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16%' }}>
                        <Text>{data?.receivedDate}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '18%' }}>
                        <Text>兹证明收到之客户</Text>
                    </TableCell>
                    <TableCell style={{ width: '32%' }}>
                        <Text>{data?.memberName}</Text>
                    </TableCell>
                    <TableCell style={{ width: '18%' }}>
                        <Text>随附配件</Text>
                    </TableCell>
                    <TableCell style={{ width: '32%' }}>
                        <Text>{data?.accessory}</Text>
                    </TableCell>
                </TableRow>
                <TableRow style={{ height: '12mm' }}>
                    <TableCell style={{ width: '18%' }}>
                        <Text>1:产品型号</Text>
                    </TableCell>
                    <TableCell style={{ width: '32%', paddingTop: 0, paddingBottom: 0 }}>
                        <Text>{data?.items[itemOffset]?.materialName}</Text>
                    </TableCell>
                    <TableCell style={{ width: '18%' }}>
                        <Text>2:产品型号</Text>
                    </TableCell>
                    <TableCell style={{ width: '32%', paddingTop: 0, paddingBottom: 0 }}>
                        <Text>{data?.items[itemOffset + 1]?.materialName}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '18%' }}>
                        <Text>1:产品序号</Text>
                    </TableCell>
                    <TableCell style={{ width: '32%' }}>
                        <Text>{data?.items[itemOffset]?.SN}</Text>
                    </TableCell>
                    <TableCell style={{ width: '18%' }}>
                        <Text>2:产品序号</Text>
                    </TableCell>
                    <TableCell style={{ width: '32%' }}>
                        <Text>{data?.items[itemOffset + 1]?.SN}</Text>
                    </TableCell>
                </TableRow>
            </Table>
            {!hiddenFooter && (
                <View style={{ margin: '8px 0' }}>
                    <ISOFooter date={docInfo.date} version={docInfo.version} fileCode={docInfo.fileCode} lang={lang} />
                </View>
            )}
        </Page>
    );
};
export default SendToRepairPageCN;
