import React from 'react';

import { Document, StyleSheet } from '@react-pdf/renderer';
import { EnumLanguage } from '../../../consts';
import SendToRepairPage from './SendToRepairPage';
import SendToRepairPageCN from './SendToRepairPageCN';
import { SendToRepairDocData } from './types';

interface Props {
    fileTitle: string;
    orderCodeBarcode?: string;
    memberCodeBarcode?: string;
    clientWebQRCode?: string;
    signature?: string;
    data?: SendToRepairDocData;
    hiddenFooter?: boolean;
    lang: EnumLanguage;
}
const SendToRepairDocument: React.FC<Props> = (props) => {
    const {
        fileTitle,
        orderCodeBarcode,
        memberCodeBarcode,
        clientWebQRCode,
        signature,
        data,
        hiddenFooter,
        lang = EnumLanguage['zh-tw'],
    } = props;
    const totalPage = data ? Math.ceil(data.items.length / 2) : 1;
    return (
        <Document>
            {lang === EnumLanguage['zh-cn'] &&
                new Array(totalPage)
                    .fill(0)
                    .map((_, i) => (
                        <SendToRepairPageCN
                            key={i}
                            fileTitle={fileTitle}
                            orderCodeBarcode={orderCodeBarcode}
                            memberCodeBarcode={memberCodeBarcode}
                            clientWebQRCode={clientWebQRCode}
                            signature={signature}
                            data={data}
                            itemOffset={i * 2}
                            hiddenFooter={hiddenFooter}
                            lang={lang}
                        />
                    ))}

            {lang === EnumLanguage['zh-tw'] &&
                new Array(totalPage)
                    .fill(0)
                    .map((_, i) => (
                        <SendToRepairPage
                            key={i}
                            fileTitle={fileTitle}
                            orderCodeBarcode={orderCodeBarcode}
                            memberCodeBarcode={memberCodeBarcode}
                            clientWebQRCode={clientWebQRCode}
                            signature={signature}
                            data={data}
                            itemOffset={i * 2}
                            hiddenFooter={hiddenFooter}
                            lang={lang}
                        />
                    ))}
        </Document>
    );
};

export default SendToRepairDocument;
