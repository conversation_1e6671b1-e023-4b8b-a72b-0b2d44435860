import React from 'react';
import { Document } from '@react-pdf/renderer';
import { EnumLanguage } from '../../../consts';
import CompletePage from './CompletePage';
import CompletePageCN from './CompletePageCN';
import { CompleteDocData } from './types';

interface Props {
    fileTitle: string;
    data?: CompleteDocData;
    signature?: string;
    hiddenFooter?: boolean;
    lang: EnumLanguage;
}
const CompleteDocument: React.FC<Props> = ({ hiddenFooter = false, ...props }) => {
    const { fileTitle, data, signature, lang = EnumLanguage['zh-tw'] } = props;

    return (
        <Document>
            {lang === EnumLanguage['zh-cn'] && (
                <>
                    <CompletePageCN
                        fileTitle={fileTitle}
                        data={data}
                        hiddenFooter={hiddenFooter}
                        signature={signature}
                        lang={lang}
                    />
                </>
            )}
            {lang === EnumLanguage['zh-tw'] && (
                <>
                    <CompletePage
                        fileTitle={fileTitle}
                        data={data}
                        hiddenFooter={hiddenFooter}
                        signature={signature}
                        lang={lang}
                    />
                </>
            )}
        </Document>
    );
};
export default CompleteDocument;
