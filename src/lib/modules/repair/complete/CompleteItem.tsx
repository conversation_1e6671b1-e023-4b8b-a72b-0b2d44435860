import React from 'react';
import numeral from 'numeral';
import { StyleSheet, Text, View } from '@react-pdf/renderer';
import { EnumLanguage } from '../../../consts';
import { Table, TableCell, TableRow } from '../../../components/table';
import CheckBox from '../../common/CheckBox';
import { CompleteItem } from './types';

const styles = StyleSheet.create({
    componentRow: {
        display: 'flex',
        flexDirection: 'row',
    },
    componentCol: {
        padding: '0px 2px',
    },
    underLine: {
        borderTop: '1px solid black',
    },
});

interface Props {
    item?: CompleteItem;
    lang: EnumLanguage;
}
const CompleteItemTable: React.FC<Props> = (props) => {
    const { item, lang } = props;
    return (
        <Table wrap={false}>
            <TableRow>
                <TableCell style={{ width: '15%' }}>
                    <View>
                        {lang === EnumLanguage['zh-cn'] && (
                            <>
                                <Text>产品编号</Text>
                            </>
                        )}
                        {lang === EnumLanguage['zh-tw'] && (
                            <>
                                <Text>產品編號</Text>
                            </>
                        )}
                        <View style={{ height: '7mm', justifyContent: 'center' }}>
                            <Text>{item?.materialCode}</Text>
                        </View>
                    </View>
                </TableCell>
                <TableCell style={{ width: '15%' }}>
                    <View>
                        {lang === EnumLanguage['zh-cn'] && (
                            <>
                                <Text>产品型号</Text>
                            </>
                        )}
                        {lang === EnumLanguage['zh-tw'] && (
                            <>
                                <Text>產品型號</Text>
                            </>
                        )}
                        <View style={{ height: '7mm', justifyContent: 'center' }}>
                            <Text>{item?.materialName}</Text>
                        </View>
                    </View>
                </TableCell>
                <TableCell style={{ width: '15%' }}>
                    <View>
                        {lang === EnumLanguage['zh-cn'] && (
                            <>
                                <Text>产品序号</Text>
                            </>
                        )}
                        {lang === EnumLanguage['zh-tw'] && (
                            <>
                                <Text>產品序號</Text>
                            </>
                        )}
                        <View style={{ height: '7mm', justifyContent: 'center' }}>
                            <Text>{item?.SN}</Text>
                        </View>
                    </View>
                </TableCell>
                <TableCell style={{ width: '15%' }}>
                    <View>
                        {lang === EnumLanguage['zh-cn'] && (
                            <>
                                <Text>耳内序号</Text>
                            </>
                        )}
                        {lang === EnumLanguage['zh-tw'] && (
                            <>
                                <Text>耳內序號</Text>
                            </>
                        )}
                        <View style={{ height: '7mm', justifyContent: 'center' }}>
                            <Text>{item?.inEarSN}</Text>
                        </View>
                    </View>
                </TableCell>
                <TableCell style={{ width: '12.5%' }}>
                    <View>
                        {lang === EnumLanguage['zh-cn'] && (
                            <>
                                <Text>购买日期</Text>
                            </>
                        )}
                        {lang === EnumLanguage['zh-tw'] && (
                            <>
                                <Text>購買日期</Text>
                            </>
                        )}
                        <View style={{ height: '7mm', justifyContent: 'center' }}>
                            <Text>{item?.shippingDate}</Text>
                        </View>
                    </View>
                </TableCell>
                <TableCell style={{ width: '12.5%' }}>
                    <View>
                        <Text>保固到期</Text>
                        <View style={{ height: '7mm', justifyContent: 'center' }}>
                            <Text>{item?.warrantyDate}</Text>
                        </View>
                    </View>
                </TableCell>
                <TableCell style={{ width: '15%' }}>
                    <View>
                        <Text>其他</Text>
                        <View style={{ height: '7mm', justifyContent: 'center' }}>
                            <Text>{item?.other}</Text>
                        </View>
                    </View>
                </TableCell>
            </TableRow>
            <TableRow>
                <TableCell style={{ width: '5%' }}>
                    {Array.from('送修原因').map((char) => (
                        <Text key={char}>{char}</Text>
                    ))}
                </TableCell>
                <TableCell style={{ width: '95%' }}>
                    <View style={{ height: '13mm', display: 'flex', flexDirection: 'row', flexWrap: 'wrap' }}>
                        {item?.reasons.map((reason: any, index: number) => (
                            <CheckBox
                                key={index}
                                style={{ marginRight: 4, marginBottom: 4 }}
                                check={reason.check}
                                label={reason.name}
                            />
                        ))}
                    </View>
                </TableCell>
            </TableRow>
            <TableRow>
                <TableCell style={{ width: '5%' }}>
                    {lang === EnumLanguage['zh-cn'] && (
                        <>
                            {Array.from('维修内容').map((char) => (
                                <Text key={char}>{char}</Text>
                            ))}
                        </>
                    )}
                    {lang === EnumLanguage['zh-tw'] && (
                        <>
                            {Array.from('維修內容').map((char) => (
                                <Text key={char}>{char}</Text>
                            ))}
                        </>
                    )}
                </TableCell>
                <TableCell style={{ width: '95%' }}>
                    <Text>{item?.maintenanceContent}</Text>
                </TableCell>
            </TableRow>
            <TableRow style={{ minHeight: '28mm' }}>
                <TableCell style={{ width: '5%' }}>
                    {Array.from('更換零件').map((char) => (
                        <Text key={char}>{char}</Text>
                    ))}
                </TableCell>
                <TableCell style={{ width: '95%', justifyContent: 'flex-start' }}>
                    <View style={styles.componentRow}>
                        <View style={{ ...styles.componentCol, width: '15%' }}>
                            {lang === EnumLanguage['zh-cn'] && (
                                <>
                                    <Text>品号</Text>
                                </>
                            )}
                            {lang === EnumLanguage['zh-tw'] && (
                                <>
                                    <Text>品號</Text>
                                </>
                            )}
                            <View style={{ ...styles.underLine }} />
                        </View>
                        <View style={{ ...styles.componentCol, width: '25%' }}>
                            <Text>品名</Text>
                            <View style={{ ...styles.underLine }} />
                        </View>
                        <View style={{ ...styles.componentCol, width: '15%', textAlign: 'right' }}>
                            {lang === EnumLanguage['zh-cn'] && (
                                <>
                                    <Text>维修数量</Text>
                                </>
                            )}
                            {lang === EnumLanguage['zh-tw'] && (
                                <>
                                    <Text>維修數量</Text>
                                </>
                            )}
                            <View style={{ ...styles.underLine }} />
                        </View>
                        <View style={{ ...styles.componentCol, width: '15%', textAlign: 'right' }}>
                            {lang === EnumLanguage['zh-cn'] && (
                                <>
                                    <Text>原币金额</Text>
                                </>
                            )}
                            {lang === EnumLanguage['zh-tw'] && (
                                <>
                                    <Text>原幣金額</Text>
                                </>
                            )}
                            <View style={{ ...styles.underLine }} />
                        </View>
                        <View style={{ ...styles.componentCol, width: '15%', textAlign: 'right' }}>
                            <Text>保固內折抵</Text>
                            <View style={{ ...styles.underLine }} />
                        </View>
                        <View style={{ ...styles.componentCol, width: '15%', textAlign: 'right' }}>
                            {lang === EnumLanguage['zh-cn'] && (
                                <>
                                    <Text>小计</Text>
                                </>
                            )}
                            {lang === EnumLanguage['zh-tw'] && (
                                <>
                                    <Text>小計</Text>
                                </>
                            )}
                            <View style={{ ...styles.underLine }} />
                        </View>
                    </View>
                    {item?.repairItems.map((repairItem, index) => (
                        <View key={index} style={{ ...styles.componentRow, margin: '1px 0' }}>
                            <View style={{ ...styles.componentCol, width: '15%' }}>
                                <Text>{repairItem.code}</Text>
                            </View>
                            <View style={{ ...styles.componentCol, width: '25%' }}>
                                <Text>{repairItem.name}</Text>
                            </View>
                            <View style={{ ...styles.componentCol, width: '15%', textAlign: 'right' }}>
                                <Text>{numeral(repairItem.qty).format('0,0')}</Text>
                            </View>
                            <View style={{ ...styles.componentCol, width: '15%', textAlign: 'right' }}>
                                <Text>{numeral(repairItem.estimatedAmount).format('0,0')}</Text>
                            </View>
                            <View style={{ ...styles.componentCol, width: '15%', textAlign: 'right' }}>
                                <Text>
                                    {repairItem.discount > 0 && numeral(repairItem.discount * -1).format('0,0')}
                                </Text>
                                <Text>{repairItem.discount == 0 && numeral(0).format('0,0')}</Text>
                            </View>
                            <View style={{ ...styles.componentCol, width: '20%', textAlign: 'right' }}>
                                <Text>{numeral(repairItem.price).format('0,0')}</Text>
                            </View>
                        </View>
                    ))}
                </TableCell>
            </TableRow>
        </Table>
    );
};
export default CompleteItemTable;
