import React from 'react';
import { Font, PDFViewer } from '@react-pdf/renderer';

import CompleteDocument from './CompleteDocument';

import { ComponentStory, ComponentMeta } from '@storybook/react';

import { CompleteDocData } from '../../..';
import { RepairCompleteMockData } from '../../../../mocks/completeDocData';

const CompleteDocumentStory: ComponentMeta<typeof CompleteDocument> = {
    title: 'Repair/Complete',
    component: CompleteDocument,
};
export default CompleteDocumentStory;

const Template: ComponentStory<typeof CompleteDocument> = (args) => (
    <PDFViewer width="100%" height="900">
        <CompleteDocument {...args} />
    </PDFViewer>
);
export const Empty = Template.bind({});
Empty.args = {
    fileTitle: '(公司名稱)',
    data: {
        items: [
            {
                reasons: [],
                repairItems: [],
            },
            {
                reasons: [],
                repairItems: [],
            },
        ],
    } as any,
};
export const Mock = Template.bind({});
Mock.args = {
    fileTitle: '科林儀器股份有限公司',
    data: RepairCompleteMockData,
    signature: '/signlong.png',
};
const TwoItemData: CompleteDocData = {
    ...RepairCompleteMockData,
    items: [RepairCompleteMockData.items[0], RepairCompleteMockData.items[0]],
};
export const TwoItem = Template.bind({});
TwoItem.args = {
    fileTitle: '科林儀器股份有限公司',
    data: TwoItemData,
};
const ThreeItemData: CompleteDocData = {
    ...RepairCompleteMockData,
    items: [RepairCompleteMockData.items[0], RepairCompleteMockData.items[0], RepairCompleteMockData.items[0]],
};
export const ThreeItem = Template.bind({});
ThreeItem.args = {
    fileTitle: '科林儀器股份有限公司',
    data: ThreeItemData,
};
