import React from 'react';
import { Image, Page, StyleSheet, Text, View, Font } from '@react-pdf/renderer';
import numeral from 'numeral';
import { Table, TableCell, TableRow } from '../../../components/table';
import { TRADITIONAL_CHINESE } from '../../../consts';
import { EnumLanguage } from '../../../consts';
import ISOFooter from '../../common/ISOFooter';
import CompleteItemTable from './CompleteItem';
import { CompleteDocData } from './types';
import path from 'path';

Font.register({
    family: TRADITIONAL_CHINESE,
    src: path.join(__dirname, '../../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
});
Font.registerHyphenationCallback((word) => word.split(''));
// Create styles
const styles = StyleSheet.create({
    page: {
        fontFamily: TRADITIONAL_CHINESE,
        fontSize: 8,
        padding: '8mm 10mm',
    },
    row: {
        display: 'flex',
        flexDirection: 'row',
    },
    footer: {
        position: 'absolute',
        bottom: 8,
        margin: '0mm 10mm',
        width: '100%',
    },
});

const docInfo = {
    date: '2023.1.1',
    version: '6',
    fileCode: 'SC-N-4-7-161',
};
interface Props {
    fileTitle: string;
    data?: CompleteDocData;
    signature?: string;
    hiddenFooter?: boolean;
    lang: EnumLanguage;
}
const CompletePage: React.FC<Props> = (props) => {
    const { fileTitle, data, signature, hiddenFooter = false, lang } = props;
    return (
        <Page size="A4" style={styles.page}>
            <View fixed>
                <Text
                    style={{ textAlign: 'right' }}
                    render={({ pageNumber, totalPages }) => `(${pageNumber} / ${totalPages})`}
                />
                <View style={{ textAlign: 'center', marginBottom: '2mm' }}>
                    <Text style={{ fontSize: '12pt', lineHeight: '1.5' }}>{fileTitle}</Text>
                    <Text>維修訂單(完成聯)</Text>
                </View>
            </View>
            <Table>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>單號</Text>
                    </TableCell>
                    <TableCell style={{ width: '33.4%' }}>
                        <Text>{data?.orderCode}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>收件人員</Text>
                    </TableCell>
                    <TableCell style={{ width: '33.4%' }}>
                        <Text>{data?.receivedUserName}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>客戶姓名</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>{data?.memberName}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.8%' }}>
                        <Text>客戶編號</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>{data?.memberCode}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>聯絡電話</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.8%' }}>
                        <Text>{data?.phone}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>收件日期</Text>
                    </TableCell>
                    <TableCell style={{ width: '33.4%' }}>
                        <Text>{data?.receivedDate}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>完成日期</Text>
                    </TableCell>
                    <TableCell style={{ width: '33.4%' }}>
                        <Text>{data?.finishDate}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>收件門市</Text>
                    </TableCell>
                    <TableCell style={{ width: '33.4%' }}>
                        <Text>{data?.receivedStoreName}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>取件門市</Text>
                    </TableCell>
                    <TableCell style={{ width: '33.4%' }}>
                        <Text>{data?.pickupStoreName}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>取件備註</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>{data?.deliveryMethod}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.8%' }}>
                        <Text>品項數量</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>{data?.qty}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>工程師</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.8%' }}>
                        <Text>{data?.repairUserName}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>隨附配件</Text>
                    </TableCell>
                    <TableCell style={{ width: '83.4%' }}>
                        <Text>{data?.accessory}</Text>
                    </TableCell>
                </TableRow>
            </Table>
            {data?.items.map((item, index) => (
                <View key={index} style={{ margin: '1mm 0' }}>
                    <CompleteItemTable item={item} lang={lang} />
                </View>
            ))}
            <Text style={{ margin: '1mm 0' }}>
                備註:機器本體經維修或經交換新品後的不良品及不良零件所有權歸本公司所有
            </Text>
            <Table wrap={false}>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>總金額(A)</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>{numeral(data?.totalAmount).format('0,0')}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.8%' }}>
                        <Text>已付金額(檢測費B)</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>{numeral(data?.receivedAmount).format('0,0')}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>應付金額(A - B)</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.8%' }}>
                        <Text>{numeral(data?.remainAmount).format('0,0')}</Text>
                    </TableCell>
                </TableRow>
            </Table>
            <View style={{ textAlign: 'right', marginTop: '2.5mm' }}>
                <Text>本人已詳閱「維修訂單(完成聯)」並同意以上所陳述內容。</Text>
            </View>
            <View style={{ ...styles.row, alignContent: 'flex-start', justifyContent: 'flex-end', marginTop: '2.5mm' }}>
                <Text style={{ marginTop: '2mm' }}>客戶取件簽名</Text>
                <View>
                    <View
                        style={{
                            minWidth: '40mm',
                            borderBottom: '1px solid black',
                            height: '8mm',
                            justifyContent: 'center',
                            padding: '0 2px 2px',
                            ...styles.row,
                        }}
                    >
                        {signature && <Image src={signature} style={{ height: '100%' }} />}
                    </View>
                    <Text style={{ marginTop: 2, fontSize: '6pt' }}>{data?.extendedWarranty}</Text>
                </View>
            </View>
            {!hiddenFooter && (
                <View style={styles.footer} fixed>
                    <ISOFooter date={docInfo.date} version={docInfo.version} fileCode={docInfo.fileCode} lang={lang} />
                </View>
            )}
        </Page>
    );
};
export default CompletePage;
