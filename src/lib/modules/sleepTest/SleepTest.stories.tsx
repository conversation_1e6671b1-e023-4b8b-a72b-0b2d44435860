import React, { useEffect, useState } from 'react';
import { Font, PDFViewer } from '@react-pdf/renderer';
import { ComponentStory, ComponentMeta } from '@storybook/react';
import SleepTestDocument from './SleepTestDocument';
import { MockSleepData } from 'src/mocks/sleepData';
import { EnumLanguage } from '../../consts';

const SleepTestStory: ComponentMeta<typeof SleepTestDocument> = {
    title: 'SleepTest/SleepTest',
    component: SleepTestDocument,
};
export default SleepTestStory;

const Template: ComponentStory<typeof SleepTestDocument> = (args) => {
    return (
        <PDFViewer width="100%" height="800">
            <SleepTestDocument {...args} />
        </PDFViewer>
    );
};
export const Empty = Template.bind({});
Empty.args = {
    hiddenFooter: false,
    lang: EnumLanguage['zh-tw'],
};
export const Mock = Template.bind({});
Mock.args = {
    hiddenFooter: false,
    sleepTest: MockSleepData,
};
export const MockChina = Template.bind({});
MockChina.args = {
    sleepTest: MockSleepData,
    lang: EnumLanguage['zh-cn'],
};
