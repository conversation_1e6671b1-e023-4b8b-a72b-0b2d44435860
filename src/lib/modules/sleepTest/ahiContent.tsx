import { Text, View } from '@react-pdf/renderer';
import React from 'react';
import { EnumLanguage } from '../../consts';
import { Table, TableCell, TableRow } from '../../components/table';

interface Props {
    data: number;
    contentTitle: string;
    unitText: string | null;
    bottomBorder: string | null;
    rangeContent: string;
    lang: EnumLanguage;
}

export const ContentHeader: React.FC<any> = (props) => {
    const { lang } = props;
    return (
        <TableRow>
            <TableCell style={{ width: '40%' }}>
                <View>
                    {lang === EnumLanguage['zh-cn'] && (
                        <>
                            <Text>名称</Text>
                        </>
                    )}
                    {lang === EnumLanguage['zh-tw'] && (
                        <>
                            <Text>名稱</Text>
                        </>
                    )}
                </View>
            </TableCell>
            <TableCell style={{ width: '20%', textAlign: 'right', borderRight: 'none' }}>
                <View>
                    {lang === EnumLanguage['zh-cn'] && (
                        <>
                            <Text>指数</Text>
                        </>
                    )}
                    {lang === EnumLanguage['zh-tw'] && (
                        <>
                            <Text>指數</Text>
                        </>
                    )}
                </View>
            </TableCell>
            <TableCell style={{ width: '10%', borderLeft: 'none' }}>
                <View></View>
            </TableCell>
            <TableCell style={{ width: '30%' }}>
                <View>
                    {lang === EnumLanguage['zh-cn'] && (
                        <>
                            <Text>正常范围</Text>
                        </>
                    )}
                    {lang === EnumLanguage['zh-tw'] && (
                        <>
                            <Text>正常範圍</Text>
                        </>
                    )}
                </View>
            </TableCell>
        </TableRow>
    );
};

export const AhiContentTable: React.FC<Props> = (props) => {
    const { contentTitle, bottomBorder, rangeContent, unitText, data } = props;
    return (
        <TableRow>
            <TableCell style={{ width: '40%' }}>
                <View>
                    <Text>{contentTitle}</Text>
                </View>
            </TableCell>
            <TableCell style={{ width: '20%', textAlign: 'right', borderRight: '' }}>
                <View>
                    <Text>{data}</Text>
                </View>
            </TableCell>

            {bottomBorder && (
                <>
                    <TableCell style={{ width: '10%', borderBottom: 'none', backgroundColor: '#D3D3D3' }}>
                        <Text>{unitText}</Text>
                    </TableCell>
                </>
            )}
            {!bottomBorder && (
                <>
                    <TableCell style={{ width: '10%', backgroundColor: '#D3D3D3' }}>
                        <Text>{unitText}</Text>
                    </TableCell>
                </>
            )}
            <TableCell style={{ width: '30%' }}>
                <View>
                    <Text>{rangeContent}</Text>
                </View>
            </TableCell>
        </TableRow>
    );
};
