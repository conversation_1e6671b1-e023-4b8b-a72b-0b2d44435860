import { Document } from '@react-pdf/renderer';
import React from 'react';
import SleepTestPage from './SleepTestPage';
import SleepTestPageCN from './SleepTestPageCN';
import { SleepTestData } from './types';
import { EnumLanguage } from '../../consts';

interface Props {
    sleepTest: SleepTestData;
    hiddenFooter?: boolean;
    lang: EnumLanguage;
}
const SleepTestDocument: React.FC<Props> = (props) => {
    const { sleepTest, hiddenFooter, lang = EnumLanguage['zh-tw'] } = props;
    return (
        <Document>
            {lang === EnumLanguage['zh-cn'] && (
                <>
                    <SleepTestPageCN sleepTest={sleepTest} hiddenFooter={hiddenFooter} lang={lang} />
                </>
            )}
            {lang === EnumLanguage['zh-tw'] && (
                <>
                    <SleepTestPage sleepTest={sleepTest} hiddenFooter={hiddenFooter} lang={lang} />
                </>
            )}
        </Document>
    );
};
export default SleepTestDocument;
