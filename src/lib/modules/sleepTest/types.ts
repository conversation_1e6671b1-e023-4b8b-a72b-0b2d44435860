export interface SleepTestData {
    id?: number;
    breathe?: {
        ahi: number; //呼吸中止指數(AHI)
        apnea: number; //阻塞指數(Apnea index)
        uai: number; //無法判定,不清楚(UAI)
        oai: number; //阻塞型中止指數(OAI)
        cai: number; //中樞型中止指數(CAI)
        mai: number; //混合型中止指數(MAI)
        hyponea: number; //低通氣指數(hyponea Index)
        snore: number; //打鼾頻率(Snore)
    };
    bloodOxygen?: {
        odi: number; //血氧低下指標
        avgBloodOxygen: number; //平均血氧濃度
        minBloodOxygen: number; //最低血氧濃度
        baseBloodOxygen: number; //基線血氧濃度
    };
    pulse?: {
        minPulse: number; //最小脈搏
        maxPulse: number; //最大脈搏
        avgPulse: number; //平均脈搏
    };
    member: ISleepTestMember; //會員資料
}

export interface ISleepTestMember {
    name: string;
    bmi?: number;
    birthday?: string;
    sex?: string;
}
