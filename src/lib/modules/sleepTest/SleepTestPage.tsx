import { Image, Page, StyleSheet, Text, View, Font } from '@react-pdf/renderer';
import React from 'react';
import { Table, TableCell, TableRow } from '../../components/table';
import { TRADITIONAL_CHINESE } from '../../consts';
import ISOFooter from '../common/ISOFooter';
import { SleepTestData } from './types';
import CheckBox from '../common/CheckBox';
import { AhiContentTable, ContentHeader } from './ahiContent';
import {
    AHITableRows,
    AHIUnitText,
    AHIBottomBorder,
    AHIRangeContent,
    BloodTableRows,
    BloodBottomBorder,
    BloodUnitText,
    BloodRangeContent,
    PulseTableRows,
    PulseBottomBorder,
    PulseUnitText,
    PulseRangeContent,
} from './consts';
import { EnumLanguage } from '../../consts';

import path from 'path';

Font.register({
    family: TRADITIONAL_CHINESE,
    fonts: [
        {
            src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'bold',
        },
        {
            src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'medium',
        },
        {
            src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'regular',
        },
    ],
});
Font.registerHyphenationCallback((word) => word.split(''));

const styles = StyleSheet.create({
    page: {
        fontFamily: TRADITIONAL_CHINESE,
        backgroundColor: 'white',
        fontSize: 10,
        padding: '8mm 10mm',
    },
    row: {
        display: 'flex',
        flexDirection: 'row',
    },
    title: {
        fontSize: 13,
        fontWeight: 'bold',
    },
    terms: {
        fontSize: 9,
        lineHeight: '1.5',
    },
});
// ISO docInfo
const docInfo = {
    date: '2023-02-08',
    version: '1',
    fileCode: 'SC-N-4-7-161',
};

interface Props {
    sleepTest: SleepTestData;
    hiddenFooter?: boolean;
    lang: EnumLanguage;
}
const SleepTestPage: React.FC<Props> = (props) => {
    const { sleepTest, hiddenFooter = false, lang } = props;

    let breatheData: number[] = [];
    let bloodOxygenData: number[] = [];
    let pulseData: number[] = [];
    if (sleepTest?.breathe) {
        breatheData = Object.values(sleepTest?.breathe);
    }
    if (sleepTest?.bloodOxygen) {
        bloodOxygenData = Object.values(sleepTest.bloodOxygen);
    }
    if (sleepTest?.pulse) {
        pulseData = Object.values(sleepTest?.pulse);
    }

    return (
        <Page size="A4" style={styles.page}>
            <View fixed>
                <View style={{ textAlign: 'center', marginBottom: '2mm' }}>
                    <Text style={{ fontSize: '24pt', lineHeight: '1.5', fontWeight: 'bold' }}>
                        ApneaLink Air 睡眠檢測報告
                    </Text>
                </View>
            </View>
            <View style={{ ...styles.row, marginTop: 17, marginBottom: 4 }}>
                <Text style={styles.title}>客戶資料</Text>
            </View>
            <Table>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>姓名:</Text>
                    </TableCell>
                    <TableCell style={{ width: '18.4%' }}>
                        <Text>{sleepTest?.member?.name}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>流水號:</Text>
                    </TableCell>
                    <TableCell style={{ width: '18.4%' }}>
                        <Text></Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>出生年月日:</Text>
                    </TableCell>
                    <TableCell style={{ width: '18.4%' }}>
                        <Text>{sleepTest?.member?.birthday}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>性別:</Text>
                    </TableCell>
                    <TableCell style={{ width: '18.4%' }}>
                        <CheckBox
                            style={{ marginRight: 4, marginLeft: 6 }}
                            check={sleepTest?.member.sex == '0'}
                            fontSize={'10pt'}
                            labelFontSize={'9pt'}
                            label=" 女"
                        />
                        <CheckBox
                            style={{ marginRight: 4, marginLeft: 40, marginTop: -10.7 }}
                            check={sleepTest?.member.sex == '1'}
                            fontSize={'10pt'}
                            labelFontSize={'9pt'}
                            label=" 男"
                        />
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>BMI :</Text>
                    </TableCell>
                    <TableCell style={{ width: '18.4%' }}>
                        <Text>{sleepTest?.member?.bmi}</Text>
                    </TableCell>

                    <TableCell style={{ width: '16.6%', borderRight: 'none' }}>
                        <Text></Text>
                    </TableCell>
                    <TableCell style={{ width: '18.4%' }}>
                        <Text></Text>
                    </TableCell>
                </TableRow>
            </Table>
            <View style={{ ...styles.row, marginTop: 17, marginBottom: 4 }}>
                <Text style={styles.title}>檢測結果</Text>
            </View>

            <Table wrap={false}>
                <ContentHeader lang={lang}></ContentHeader>
                {AHITableRows.map((item, index) => (
                    <AhiContentTable
                        data={breatheData[index]}
                        contentTitle={item}
                        key={index}
                        bottomBorder={AHIBottomBorder[index]}
                        rangeContent={AHIRangeContent[index]}
                        unitText={AHIUnitText[index]}
                        lang={lang}
                    />
                ))}
            </Table>

            <View style={{ ...styles.row, marginTop: 17, marginBottom: 6 }}>
                <Text style={styles.title}></Text>
            </View>

            <Table wrap={false}>
                <ContentHeader lang={lang}></ContentHeader>
                {BloodTableRows.map((item, index) => (
                    <AhiContentTable
                        data={bloodOxygenData[index]}
                        contentTitle={item}
                        key={index}
                        bottomBorder={BloodBottomBorder[index]}
                        rangeContent={BloodRangeContent[index]}
                        unitText={BloodUnitText[index]}
                        lang={lang}
                    />
                ))}
            </Table>

            <View style={{ ...styles.row, marginTop: 17, marginBottom: 6 }}>
                <Text style={styles.title}></Text>
            </View>

            <Table wrap={false}>
                <ContentHeader lang={lang}></ContentHeader>
                {PulseTableRows.map((item, index) => (
                    <AhiContentTable
                        data={pulseData[index]}
                        contentTitle={item}
                        key={index}
                        bottomBorder={PulseBottomBorder[index]}
                        rangeContent={PulseRangeContent[index]}
                        unitText={PulseUnitText[index]}
                        lang={lang}
                    />
                ))}
            </Table>

            {!hiddenFooter && (
                <View style={{ marginTop: '42px' }}>
                    <ISOFooter date={docInfo.date} version={docInfo.version} fileCode={docInfo.fileCode} lang={lang} />
                </View>
            )}
        </Page>
    );
};
export default SleepTestPage;
