export interface EarModelOrderDocData {
    code?: string;
    orderCode?: string;
    orderType?: string;
    date?: string;
    expectedCompletedDate?: string;
    createStore?: string;
    createUser?: string;
    recipientAddress?: string;
    recipientStore?: string;
    recipientUser?: string;
    memberCode?: string;
    memberName?: string;
    memberAge?: string;
    leftContent?: EarContentData;
    rightContent?: EarContentData;
    processes?: ProcessData[];
    isRemake?: boolean;
}

export interface EarContentData {
    productionSn?: string;
    earLossLevel?: string;
    250?: string;
    500?: string;
    1000?: string;
    2000?: string;
    4000?: string;
    8000?: string;
    orderType?: string;
    content?: string;
    remark?: string;
}

export interface ProcessData {
    name?: string;
    status?: string;
    user?: string;
    date?: string;
}
