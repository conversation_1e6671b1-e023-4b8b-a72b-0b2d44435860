import React from 'react';
import { Table, TableCell, TableRow } from '../../components/table';
import { EnumLanguage } from '../../consts';
import { ProcessData } from './types';
import { Text } from '@react-pdf/renderer';

interface Props {
    processes?: ProcessData[];
    lang: EnumLanguage;
}
const ProcessTable: React.FC<Props> = ({ processes = [], lang }) => {
    if (processes.length > 8) {
        throw Error(`EarModelOrderProcess can't be more than 8 items.s`);
    }
    return (
        <Table>
            <TableRow>
                <TableCell style={{ width: '100%', backgroundColor: '#c5c5c5' }}>
                    {lang === EnumLanguage['zh-cn'] && (
                        <>
                            <Text>制作工序流程</Text>
                        </>
                    )}
                    {lang === EnumLanguage['zh-tw'] && (
                        <>
                            <Text>製作工序流程 morear Use Only</Text>
                        </>
                    )}
                </TableCell>
            </TableRow>
            {Array.from({ length: 8 }, (_, index) => (
                <TableRow key={index} style={{ height: '10mm' }}>
                    <TableCell dense style={{ width: '30%' }}>
                        <Text>{processes[index]?.name}</Text>
                    </TableCell>
                    <TableCell dense style={{ width: '10%', textAlign: 'center' }}>
                        <Text>{processes[index]?.status}</Text>
                    </TableCell>
                    <TableCell dense style={{ width: '30%' }}>
                        <Text>{processes[index]?.user}</Text>
                    </TableCell>
                    <TableCell dense style={{ width: '30%' }}>
                        <Text>{processes[index]?.date}</Text>
                    </TableCell>
                </TableRow>
            ))}
        </Table>
    );
};
export default ProcessTable;
