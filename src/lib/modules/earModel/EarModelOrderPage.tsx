import { Image, Page, StyleSheet, Text, View, Font } from '@react-pdf/renderer';
import React from 'react';
import { Table, TableCell, TableRow } from '../../components/table';
import { TRADITIONAL_CHINESE } from '../../consts';
import ISOFooter from '../common/ISOFooter';
import EarContentTable from './EarContentTable';
import { Base64Header } from './image';
import ProcessTable from './ProcessTable';
import { EarModelOrderDocData } from './types';
import path from 'path';
import { EnumLanguage } from '../../consts';

Font.register({
    family: TRADITIONAL_CHINESE,
    src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
});

Font.registerHyphenationCallback((word) => word.split(''));

const styles = StyleSheet.create({
    page: {
        fontFamily: TRADITIONAL_CHINESE,
        backgroundColor: 'white',
        fontSize: 10,
        padding: '8mm 10mm',
    },
    content: {},
    row: {
        display: 'flex',
        flexDirection: 'row',
    },
    title: {
        fontSize: 12,
        color: 'rgb(244, 160, 0)',
    },
    remake: {
        fontSize: 16,
        border: 1,
        borderColor: 'rgb(256, 0, 0)',
        color: 'rgb(256, 0, 0)',
        position: 'absolute',
        right: 25,
        top: 25,
    },
    fillField: {
        margin: '0 8px',
        padding: '0 8px',
        borderBottom: '1px solid black',
    },
    footer: {
        position: 'absolute',
        bottom: 8,
        margin: '0mm 10mm',
        width: '100%',
    },
});
const docInfo = {
    date: '2020.11.30',
    version: '1',
    fileCode: 'SC-N-4-7-186',
};
interface Props {
    earModelOrder?: EarModelOrderDocData;
    lang: EnumLanguage;
}
const EarModelOrderPage: React.FC<Props> = (props) => {
    const { earModelOrder, lang } = props;
    return (
        <Page size="A4" style={styles.page}>
            <View style={{ margin: '-8mm -10mm 4mm' }}>
                <Image src={Base64Header} />
                {earModelOrder?.isRemake && <Text style={styles.remake}>重製</Text>}
            </View>
            <View style={styles.content}>
                <View style={{ ...styles.row, marginBottom: 8 }}>
                    <View style={{ flex: 1 }}>
                        <Text style={styles.title}>基本資料</Text>
                    </View>
                    <View style={styles.row}>
                        <Text>單號:</Text>
                        <Text style={styles.fillField}>{earModelOrder?.code}</Text>
                        <Text>管理編號:</Text>
                        <Text style={styles.fillField}>{earModelOrder?.orderCode}</Text>
                    </View>
                </View>
                <Table>
                    <TableRow>
                        <TableCell style={{ width: '16.6%' }}>
                            <Text>訂單類別</Text>
                        </TableCell>
                        <TableCell style={{ width: '83.4%' }}>
                            <Text>{earModelOrder?.orderType}</Text>
                        </TableCell>
                    </TableRow>
                    <TableRow>
                        <TableCell style={{ width: '16.6%' }}>
                            <Text>訂單日期</Text>
                        </TableCell>
                        <TableCell style={{ width: '33.4%' }}>
                            <Text>{earModelOrder?.date}</Text>
                        </TableCell>
                        <TableCell style={{ width: '16.6%' }}>
                            <Text>預計完成日期</Text>
                        </TableCell>
                        <TableCell style={{ width: '33.4%' }}>
                            <Text>{earModelOrder?.expectedCompletedDate}</Text>
                        </TableCell>
                    </TableRow>
                    <TableRow>
                        <TableCell style={{ width: '16.6%' }}>
                            <Text>訂單來源</Text>
                        </TableCell>
                        <TableCell style={{ width: '33.4%' }}>
                            <Text>{earModelOrder?.createStore}</Text>
                        </TableCell>
                        <TableCell style={{ width: '16.6%' }}>
                            <Text>開單人員</Text>
                        </TableCell>
                        <TableCell style={{ width: '33.4%' }}>
                            <Text>{earModelOrder?.createUser}</Text>
                        </TableCell>
                    </TableRow>
                    <TableRow>
                        <TableCell style={{ width: '16.6%' }}>
                            <Text>收件地址</Text>
                        </TableCell>
                        <TableCell style={{ width: '83.4%' }}>
                            <Text>{earModelOrder?.recipientAddress}</Text>
                        </TableCell>
                    </TableRow>
                    <TableRow>
                        <TableCell style={{ width: '16.6%' }}>
                            <Text>收件門市</Text>
                        </TableCell>
                        <TableCell style={{ width: '33.4%' }}>
                            <Text>{earModelOrder?.recipientStore}</Text>
                        </TableCell>
                        <TableCell style={{ width: '16.6%' }}>
                            <Text>收件人</Text>
                        </TableCell>
                        <TableCell style={{ width: '33.4%' }}>
                            <Text>{earModelOrder?.recipientUser}</Text>
                        </TableCell>
                    </TableRow>
                </Table>
                <View style={{ ...styles.row, marginTop: 8, marginBottom: 8 }}>
                    <Text style={styles.title}>訂製內容</Text>
                </View>
                <Table>
                    <TableRow>
                        <TableCell style={{ width: '16.6%' }}>
                            <Text>客戶編號</Text>
                        </TableCell>
                        <TableCell style={{ width: '16.7%' }}>
                            <Text>{earModelOrder?.memberCode}</Text>
                        </TableCell>
                        <TableCell style={{ width: '16.6%' }}>
                            <Text>客戶姓名</Text>
                        </TableCell>
                        <TableCell style={{ width: '16.7%' }}>
                            <Text>{earModelOrder?.memberName}</Text>
                        </TableCell>
                        <TableCell style={{ width: '16.6%' }}>
                            <Text>客戶年齡</Text>
                        </TableCell>
                        <TableCell style={{ width: '16.8%' }}>
                            <Text>{earModelOrder?.memberAge}</Text>
                        </TableCell>
                    </TableRow>
                </Table>
                <View style={styles.row}>
                    <View style={{ width: '50%' }}>
                        <EarContentTable ear="左耳 LEFT" content={earModelOrder?.leftContent} lang={lang} />
                    </View>
                    <View style={{ width: '50%' }}>
                        <EarContentTable ear="右耳 RIGHT" content={earModelOrder?.rightContent} lang={lang} />
                    </View>
                </View>
                <ProcessTable processes={earModelOrder?.processes} lang={lang} />
                <View style={{ margin: '4px 0', fontSize: 8 }}>
                    <Text>備註: 1. 此表單填寫說明請參照【取耳型(模)辦法】(SC-N-3-7-054)</Text>
                </View>
            </View>
            <View style={styles.footer} fixed>
                <ISOFooter date={docInfo.date} version={docInfo.version} fileCode={docInfo.fileCode} lang={lang} />
            </View>
            <View style={{ position: 'absolute', right: '4mm', bottom: '22mm', fontSize: 8 }}>
                <Text>{Array.from('受理訂製後，請於當日將本訂製單與耳型寄出給秘書收執。').join('\n')}</Text>
            </View>
        </Page>
    );
};
export default EarModelOrderPage;
