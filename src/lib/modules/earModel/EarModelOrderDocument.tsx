import { Document } from '@react-pdf/renderer';
import React from 'react';
import EarModelOrderPage from './EarModelOrderPage';
import EarModelOrderPageCN from './EarModelOrderPageCN';
import { EnumLanguage } from '../../consts';
import { EarModelOrderDocData } from './types';

interface Props {
    earModelOrder?: EarModelOrderDocData;
    lang: EnumLanguage;
}
const EarModelOrderDocument: React.FC<Props> = (props) => {
    const { earModelOrder, lang = EnumLanguage['zh-tw'] } = props;
    return (
        <Document>
            {lang === EnumLanguage['zh-cn'] && (
                <>
                    <EarModelOrderPageCN earModelOrder={earModelOrder} lang={lang} />
                </>
            )}
            {lang === EnumLanguage['zh-tw'] && (
                <>
                    <EarModelOrderPage earModelOrder={earModelOrder} lang={lang} />
                </>
            )}
        </Document>
    );
};
export default EarModelOrderDocument;
