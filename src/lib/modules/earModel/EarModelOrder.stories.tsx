import React, { useEffect, useState } from 'react';
import { Font, PDFViewer } from '@react-pdf/renderer';
import { ComponentStory, ComponentMeta } from '@storybook/react';
import EarModelOrderDocument from './EarModelOrderDocument';
import { MockEarModelOrderData } from 'src/mocks/earModelOrderData';

const EarModelOrderStory: ComponentMeta<typeof EarModelOrderDocument> = {
    title: 'Morear/EarModelOrder',
    component: EarModelOrderDocument,
};
export default EarModelOrderStory;

const Template: ComponentStory<typeof EarModelOrderDocument> = (args) => {
    return (
        <PDFViewer width="100%" height="800">
            <EarModelOrderDocument {...args} />
        </PDFViewer>
    );
};
export const Empty = Template.bind({});
Empty.args = {};
export const Mock = Template.bind({});
Mock.args = {
    earModelOrder: MockEarModelOrderData,
};
