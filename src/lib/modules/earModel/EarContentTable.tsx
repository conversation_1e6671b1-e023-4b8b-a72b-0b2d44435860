import { Text } from '@react-pdf/renderer';
import React from 'react';
import { EnumLanguage } from '../../consts';
import { Table, TableCell, TableRow } from '../../components/table';
import { EarContentData } from './types';

interface Props {
    ear: string;
    content?: EarContentData;
    lang: EnumLanguage;
}
const EarContentTable: React.FC<Props> = (props) => {
    const { ear, content, lang } = props;
    return (
        <Table style={{ height: '80mm' }}>
            <TableRow>
                <TableCell dense style={{ width: '100%', backgroundColor: '#c5c5c5' }}>
                    <Text style={{ textAlign: 'center' }}>{ear}</Text>
                </TableCell>
            </TableRow>
            <TableRow>
                <TableCell dense style={{ width: '33.3%' }}>
                    {lang === EnumLanguage['zh-cn'] && (
                        <>
                            <Text>制作编号</Text>
                        </>
                    )}
                    {lang === EnumLanguage['zh-tw'] && (
                        <>
                            <Text>製作編號</Text>
                        </>
                    )}
                </TableCell>
                <TableCell dense style={{ width: '66.7%' }}>
                    <Text>{content?.productionSn}</Text>
                </TableCell>
            </TableRow>
            <TableRow style={{ textAlign: 'center', height: '10mm' }}>
                <TableCell dense style={{ width: '16%' }}>
                    {lang === EnumLanguage['zh-cn'] && (
                        <>
                            <Text>{'听损\n程度'}</Text>
                        </>
                    )}
                    {lang === EnumLanguage['zh-tw'] && (
                        <>
                            <Text>{'聽損\n程度'}</Text>
                        </>
                    )}
                </TableCell>
                <TableCell dense style={{ width: '14%' }}>
                    <Text>250</Text>
                </TableCell>
                <TableCell dense style={{ width: '14%' }}>
                    <Text>500</Text>
                </TableCell>
                <TableCell dense style={{ width: '14%' }}>
                    <Text>1K</Text>
                </TableCell>
                <TableCell dense style={{ width: '14%' }}>
                    <Text>2K</Text>
                </TableCell>
                <TableCell dense style={{ width: '14%' }}>
                    <Text>4K</Text>
                </TableCell>
                <TableCell dense style={{ width: '14%' }}>
                    <Text>8K</Text>
                </TableCell>
            </TableRow>
            <TableRow style={{ textAlign: 'center', height: '10mm' }}>
                <TableCell dense style={{ width: '16%' }}>
                    <Text>{content?.earLossLevel}</Text>
                </TableCell>
                <TableCell dense style={{ width: '14%' }}>
                    <Text>{content?.[250]}</Text>
                </TableCell>
                <TableCell dense style={{ width: '14%' }}>
                    <Text>{content?.[500]}</Text>
                </TableCell>
                <TableCell dense style={{ width: '14%' }}>
                    <Text>{content?.[1000]}</Text>
                </TableCell>
                <TableCell dense style={{ width: '14%' }}>
                    <Text>{content?.[2000]}</Text>
                </TableCell>
                <TableCell dense style={{ width: '14%' }}>
                    <Text>{content?.[4000]}</Text>
                </TableCell>
                <TableCell dense style={{ width: '14%' }}>
                    <Text>{content?.[8000]}</Text>
                </TableCell>
            </TableRow>
            <TableRow>
                <TableCell dense style={{ width: '33.3%' }}>
                    {lang === EnumLanguage['zh-cn'] && (
                        <>
                            <Text>订单类别</Text>
                        </>
                    )}
                    {lang === EnumLanguage['zh-tw'] && (
                        <>
                            <Text>訂單類別</Text>
                        </>
                    )}
                </TableCell>
                <TableCell dense style={{ width: '66.7%' }}>
                    <Text>{content?.orderType}</Text>
                </TableCell>
            </TableRow>
            <TableRow style={{ height: '30mm' }}>
                <TableCell dense style={{ width: '100%', justifyContent: 'flex-start' }}>
                    <Text>{content?.content}</Text>
                </TableCell>
            </TableRow>
            <TableRow style={{ height: '25mm' }}>
                <TableCell dense style={{ width: '100%', justifyContent: 'flex-start' }}>
                    {lang === EnumLanguage['zh-cn'] && (
                        <>
                            <Text>备注</Text>
                        </>
                    )}
                    {lang === EnumLanguage['zh-tw'] && (
                        <>
                            <Text>備註</Text>
                        </>
                    )}
                    <Text>{content?.remark}</Text>
                </TableCell>
            </TableRow>
        </Table>
    );
};
export default EarContentTable;
