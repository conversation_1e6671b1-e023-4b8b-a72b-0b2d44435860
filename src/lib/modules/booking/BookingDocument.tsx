import { Document } from '@react-pdf/renderer';
import React from 'react';
import BookingPage from './BookingPage';
import BookingPageCN from './BookingPageCN';
import { BookingDocData } from './types';
import { EnumLanguage } from '../../consts';

interface Props {
    companyTitle?: string;
    booking?: BookingDocData;
    hiddenFooter?: boolean;
    lang: EnumLanguage;
}
const BookingDocument: React.FC<Props> = (props) => {
    const { companyTitle, booking, hiddenFooter, lang = EnumLanguage['zh-tw'] } = props;
    return (
        <Document>
            {lang === EnumLanguage['zh-cn'] && (
                <>
                    <BookingPageCN booking={booking} hiddenFooter={hiddenFooter} lang={lang} />
                    <BookingPageCN booking={booking} hiddenFooter={hiddenFooter} lang={lang} />
                </>
            )}
            {lang === EnumLanguage['zh-tw'] && (
                <>
                    <BookingPage
                        companyTitle={companyTitle}
                        booking={booking}
                        hiddenFooter={hiddenFooter}
                        lang={lang}
                    />
                    <BookingPage
                        companyTitle={companyTitle}
                        booking={booking}
                        hiddenFooter={hiddenFooter}
                        lang={lang}
                    />
                </>
            )}
        </Document>
    );
};
export default BookingDocument;
