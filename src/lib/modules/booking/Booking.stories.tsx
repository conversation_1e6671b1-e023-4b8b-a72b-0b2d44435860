import React, { useEffect, useState } from 'react';
import { Font, PDFViewer } from '@react-pdf/renderer';
import { ComponentStory, ComponentMeta } from '@storybook/react';
import BookingDocument from './BookingDocument';
import { MockBookingData } from 'src/mocks/bookingData';

const BookingStory: ComponentMeta<typeof BookingDocument> = {
    title: 'Booking/Booking',
    component: BookingDocument,
};
export default BookingStory;

const Template: ComponentStory<typeof BookingDocument> = (args) => {
    return (
        <PDFViewer width="100%" height="800">
            <BookingDocument {...args} />
        </PDFViewer>
    );
};
export const Empty = Template.bind({});
Empty.args = {
    companyTitle: '(公司名)',
    hiddenFooter: true,
};
export const Mock = Template.bind({});
Mock.args = {
    companyTitle: '(公司名)',
    booking: MockBookingData,
};
