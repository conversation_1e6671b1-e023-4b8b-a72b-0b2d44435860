import React from 'react';
import numeral from 'numeral';
import { StyleSheet, Text, View } from '@react-pdf/renderer';
import { Table, TableCell, TableRow } from '../../components/table';
import { IRentDevice } from './types';

const styles = StyleSheet.create({
    componentRow: {
        display: 'flex',
        flexDirection: 'row',
    },
    componentCol: {
        padding: '0px 2px',
    },
    underLine: {
        borderTop: '1px solid black',
    },
});

interface Props {
    rentDevice?: IRentDevice;
}
const RentDeviceTable: React.FC<Props> = (props) => {
    const { rentDevice } = props;
    return (
        <TableRow style={{ height: '12mm' }}>
            <TableCell style={{ width: '15%' }}>
                <View style={{ justifyContent: 'center' }}>
                    <Text>{rentDevice && rentDevice.type}</Text>
                </View>
            </TableCell>
            <TableCell style={{ width: '40%' }}>
                <View style={{ justifyContent: 'center' }}>
                    <Text>{rentDevice && rentDevice.name}</Text>
                </View>
            </TableCell>
            <TableCell style={{ width: '20%' }}>
                <View style={{ justifyContent: 'center' }}>
                    <Text>{rentDevice && rentDevice.SN}</Text>
                </View>
            </TableCell>
            <TableCell style={{ width: '10%' }}>
                <View style={{ justifyContent: 'center' }}>
                    <Text>{rentDevice && '1'}</Text>
                </View>
            </TableCell>
            <TableCell style={{ width: '15%' }}>
                <View style={{ justifyContent: 'center' }}>
                    <Text>{rentDevice && Number(rentDevice.price) > 0 && numeral(rentDevice.price).format('0,0')}</Text>
                </View>
            </TableCell>
        </TableRow>
    );
};
export default RentDeviceTable;
