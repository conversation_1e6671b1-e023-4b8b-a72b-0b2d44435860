import { Image, Page, StyleSheet, Text, View, Font } from '@react-pdf/renderer';
import React from 'react';
import { Table, TableCell, TableRow } from '../../components/table';
import { TRADITIONAL_CHINESE } from '../../consts';
import ISOFooter from '../common/ISOFooter';
import { BookingDocData } from './types';
import RentDeviceTable from './BookingItem';
import CheckBox from '../common/CheckBox';
import { EnumLanguage } from '../../consts';

import path from 'path';

Font.register({
    family: TRADITIONAL_CHINESE,
    fonts: [
        {
            src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'bold',
        },
        {
            src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'medium',
        },
        {
            src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'regular',
        },
    ],
});

Font.registerHyphenationCallback((word) => word.split(''));

const tableRows = [0, 1, 2];
const styles = StyleSheet.create({
    page: {
        fontFamily: TRADITIONAL_CHINESE,
        backgroundColor: 'white',
        fontSize: 9,
        padding: '6mm 8mm',
    },
    row: {
        display: 'flex',
        flexDirection: 'row',
    },
    title: {
        fontSize: 11,
        fontWeight: 'bold',
    },
    terms: {
        fontSize: 9,
        lineHeight: '1.5',
    },
});
// TODO ISO文件審核的版本號,等待審核完畢要回來更新
const docInfo = {
    date: '2023-02-28',
    version: '1',
    fileCode: 'C-SC-N-4-7-036',
};

interface Props {
    companyTitle?: string;
    booking?: BookingDocData;
    hiddenFooter?: boolean;
    lang: EnumLanguage;
}
const BookingPageCN: React.FC<Props> = (props) => {
    const { companyTitle, booking, hiddenFooter = false, lang } = props;

    return (
        <Page size="A4" style={styles.page}>
            <Text
                style={{ textAlign: 'right', fontSize: '6pt' }}
                fixed
                render={({ pageNumber }) => pageNumber === 1 && `第一联(门市人员收执)`}
            />
            <Text
                style={{ textAlign: 'right', fontSize: '6pt' }}
                fixed
                render={({ pageNumber }) => pageNumber === 2 && `第二联(用户留存收执)`}
            />
            <View fixed>
                <View style={{ textAlign: 'center', marginBottom: '1mm' }}>
                    <Text style={{ fontSize: '20pt', lineHeight: '1.5', fontWeight: 'bold' }}>暂借切结书</Text>
                </View>
            </View>
            <Table>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>用户姓名(甲方):</Text>
                    </TableCell>
                    <TableCell style={{ width: '31.8%' }}>
                        <Text>{booking?.member?.name}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>会 员 编 号:</Text>
                    </TableCell>
                    <TableCell style={{ width: '35%' }}>
                        <Text>{booking?.member?.memberCode}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>联 络 地 址:</Text>
                    </TableCell>
                    <TableCell style={{ width: '48.4%' }}>
                        <Text>{booking?.member?.address}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>联 络 电 话:</Text>
                    </TableCell>
                    <TableCell style={{ width: '18.4%' }}>
                        <Text>{booking?.member?.mobile || booking?.member?.phone}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>借 出 日 期:</Text>
                    </TableCell>
                    <TableCell style={{ width: '31.8%' }}>
                        <Text>{booking?.rentStartDate}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>归 还 日 期:</Text>
                    </TableCell>
                    <TableCell style={{ width: '35%' }}>
                        <Text>{booking?.rentEndDate}</Text>
                    </TableCell>
                </TableRow>
            </Table>
            <View style={{ ...styles.row, marginTop: 12, marginBottom: 3 }}>
                <Text style={styles.title}>一、暂借产品品项</Text>
            </View>
            <Table wrap={false}>
                <TableRow>
                    <TableCell style={{ width: '15%' }}>
                        <View>
                            <Text>产品</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '40%' }}>
                        <View>
                            <Text>型号</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>
                        <View>
                            <Text>机器序号</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '10%' }}>
                        <View>
                            <Text>数量</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '15%' }}>
                        <View>
                            <Text>原价</Text>
                        </View>
                    </TableCell>
                </TableRow>
                {tableRows.map((item, index) => (
                    <RentDeviceTable rentDevice={booking?.rentDevice[index]} key={index} />
                ))}
            </Table>
            <View style={{ ...styles.row, marginTop: 12, marginBottom: 4 }}>
                <Text style={styles.title}>二、暂借目的区分(勾选)</Text>
            </View>
            <View style={{ width: '100%', display: 'flex', flexDirection: 'row' }}>
                <CheckBox
                    style={{ marginRight: 4, lineHeight: 1.7 }}
                    check={false}
                    fontSize={'12pt'}
                    labelFontSize={'9pt'}
                    label=" 甲方于未购买前须暂借设备体验，经双方同意，适用本切结书三、条款第1、2条及第6条至第10条。"
                />
            </View>
            <View style={{ width: '100%', display: 'flex', flexDirection: 'row', marginTop: 5 }}>
                <CheckBox
                    style={{ marginRight: 4, lineHeight: 1.7 }}
                    check={false}
                    fontSize={'12pt'}
                    labelFontSize={'9pt'}
                    label=" 甲方设备送修，经乙方评估后须暂借设备替代使用，经双方同意，适用本切结书三、条款第2条至第10条。"
                />
            </View>
            <View style={{ ...styles.row, marginTop: 12, marginBottom: 4 }}>
                <Text style={styles.title}>三、條款</Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    1.甲方确认已收到乙方出借以上各项设备货品，并确认其外观及功能均属完整良好，且经乙方人员说明指导后，已充分了解相关操作方式。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>2.甲方已提供身份证正反面复印件。</Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    3.因甲方原设备损坏送修，乙方于维修期间出借暂借设备供替代使用，其型号、功能及规格以乙方当时现有设备为准，甲方应予配合使用。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    4.原设备维修完成后，乙方应通知甲方约定取件日期，甲方应依约至乙方借出门市取回设备并完成维修付款程序，同时归还暂借设备。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    5.甲方于收到乙方报价通知后，应于七个工作天内回覆是否同意维修，或另行与乙方约定回覆期限。乙方得自报价日起保留该送修原设备最长三十
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    个工作天，如因甲方维修项目涉及零件待料，则双方应另行约定保留期。于保留期过后，甲方仍无回覆是否维修或取件，则甲方同意乙方得视同
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    甲方不进行维修，原设备将另行暂时保管并通知甲方再确认处置方式，必要时得收取保管费用。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    6.双方同意在上述归还日期由甲方归还暂借之各项设备，如甲方未于归还日期前归还所有设备货品，视为遗失，应依本暂借合约书第一条所列之售
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>价赔偿。</Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    7.甲方应善尽对暂借设备之保管义务。如有遗失情形，应依本暂借合约书第一条所列之售价赔偿。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    8.暂借期间，如设备发生非人为损坏，甲方应立即通知乙方更换设备。乙方经检测并合理判定为人为损坏，并提供相关说明后，甲方应负全部修
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>复责任，但以本暂借合约书第一条所列之售价为上限。</Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    9.甲方应善尽对日常保养及维护之责任，归还暂借设备时，须经乙方确认暂借设备外观与功能无误，并应由双方签名确认。如未经双方签署确认，
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>视为尚未完成归还。</Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    10.本暂借合约书因履行所生之一切争议，双方同意以中华民国法律为准据法，并以乙方借出门市所在地地方法院为第一审管辖法院。惟甲方为消费
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 12 }}>者者，仍得依消费者保护法第47条规定进行。</Text>
            </View>
            <Table style={{ marginTop: 6, marginBottom: 4 }}>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>暂借者(甲方):</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20.4%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            {/* <Text style={{ fontWeight: 'medium' }}>借出者(乙方):</Text> */}
                            <Text>借出者(乙方):</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20.4%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '12%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>门市:</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '14%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '100%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>
                                暂借者(甲方): ____________________
                                已归还上述各项货品，并已收到上述全数押金金额及身份证正反面复印件
                            </Text>
                        </View>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>收货者:</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20.4%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>门市:</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20.4%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '12%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>日期:</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '14%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                </TableRow>
            </Table>
            {!hiddenFooter && (
                <View style={{ margin: '8px 0' }}>
                    <ISOFooter date={docInfo.date} version={docInfo.version} fileCode={docInfo.fileCode} lang={lang} />
                </View>
            )}
        </Page>
    );
};
export default BookingPageCN;
