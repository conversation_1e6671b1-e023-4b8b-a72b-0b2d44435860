import { Image, Page, StyleSheet, Text, View, Font } from '@react-pdf/renderer';
import React from 'react';
import { Table, TableCell, TableRow } from '../../components/table';
import { TRADITIONAL_CHINESE } from '../../consts';
import ISOFooter from '../common/ISOFooter';
import { BookingDocData } from './types';
import RentDeviceTable from './BookingItem';
import CheckBox from '../common/CheckBox';
import { EnumLanguage } from '../../consts';

import path from 'path';

Font.register({
    family: TRADITIONAL_CHINESE,
    fonts: [
        {
            src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'bold',
        },
        {
            src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'medium',
        },
        {
            src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'regular',
        },
    ],
});

Font.registerHyphenationCallback((word) => word.split(''));

const tableRows = [0, 1, 2];
const styles = StyleSheet.create({
    page: {
        fontFamily: TRADITIONAL_CHINESE,
        backgroundColor: 'white',
        fontSize: 9,
        padding: '8mm 10mm',
    },
    row: {
        display: 'flex',
        flexDirection: 'row',
    },
    title: {
        fontSize: 11,
        fontWeight: 'bold',
    },
    terms: {
        fontSize: 9,
        lineHeight: '1.5',
    },
});
// TODO ISO文件審核的版本號,等待審核完畢要回來更新
const docInfo = {
    date: '2023-02-28',
    version: '1',
    fileCode: 'C-SC-N-4-7-036',
};

interface Props {
    companyTitle?: string;
    booking?: BookingDocData;
    hiddenFooter?: boolean;
    lang: EnumLanguage;
}
const BookingPageCN: React.FC<Props> = (props) => {
    const { companyTitle, booking, hiddenFooter = false, lang } = props;

    return (
        <Page size="A4" style={styles.page}>
            <Text
                style={{ textAlign: 'right', fontSize: '6pt' }}
                fixed
                render={({ pageNumber }) => pageNumber === 1 && `第一联(门市人员收执)`}
            />
            <Text
                style={{ textAlign: 'right', fontSize: '6pt' }}
                fixed
                render={({ pageNumber }) => pageNumber === 2 && `第二联(用户留存收执)`}
            />
            <View fixed>
                <View style={{ textAlign: 'center', marginBottom: '2mm' }}>
                    <Text style={{ fontSize: '20pt', lineHeight: '1.5', fontWeight: 'bold' }}>暂借切结书</Text>
                </View>
            </View>
            <Table>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>用户姓名(甲方):</Text>
                    </TableCell>
                    <TableCell style={{ width: '48.4%' }}>
                        <Text>{booking?.member?.name}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>身 份 证 号:</Text>
                    </TableCell>
                    <TableCell style={{ width: '18.4%' }}>
                        <Text></Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>联 络 地 址:</Text>
                    </TableCell>
                    <TableCell style={{ width: '48.4%' }}>
                        <Text>{booking?.member?.address}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>联 络 电 话:</Text>
                    </TableCell>
                    <TableCell style={{ width: '18.4%' }}>
                        <Text>{booking?.member?.mobile || booking?.member?.phone}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>借 出 日 期:</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>{booking?.rentStartDate}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>预 计 归 还 日:</Text>
                    </TableCell>
                    <TableCell style={{ width: '15.2%' }}>
                        <Text>{booking?.rentEndDate}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>实 际 归 还 日:</Text>
                    </TableCell>
                    <TableCell style={{ width: '18.4%' }}>
                        <Text></Text>
                    </TableCell>
                </TableRow>
            </Table>
            <View style={{ ...styles.row, marginTop: 16, marginBottom: 4 }}>
                <Text style={styles.title}>一、暂借产品品项</Text>
            </View>
            <Table wrap={false}>
                <TableRow>
                    <TableCell style={{ width: '15%' }}>
                        <View>
                            <Text>产品</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '40%' }}>
                        <View>
                            <Text>型号</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>
                        <View>
                            <Text>机器序号</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '10%' }}>
                        <View>
                            <Text>数量</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '15%' }}>
                        <View>
                            <Text>原价</Text>
                        </View>
                    </TableCell>
                </TableRow>
                {tableRows.map((item, index) => (
                    <RentDeviceTable rentDevice={booking?.rentDevice[index]} key={index} />
                ))}
            </Table>
            <View style={{ ...styles.row, marginTop: 14, marginBottom: 6 }}>
                <Text style={styles.title}>二、暂借目的区别</Text>
            </View>
            <View style={{ width: '100%', display: 'flex', flexDirection: 'row' }}>
                <CheckBox
                    style={{ marginRight: 4, lineHeight: 1.7 }}
                    check={false}
                    fontSize={'12pt'}
                    labelFontSize={'9pt'}
                    label=" 甲方于未购买前须暂借设备体验，经双方同意，适用本切结书三、条款第1、2条及第6条至第10条。"
                />
            </View>
            <View style={{ width: '100%', display: 'flex', flexDirection: 'row', marginTop: 5 }}>
                <CheckBox
                    style={{ marginRight: 4, lineHeight: 1.7 }}
                    check={false}
                    fontSize={'12pt'}
                    labelFontSize={'9pt'}
                    label=" 甲方设备送修，经乙方评估后须暂借设备替代使用，经双方同意，适用本切结书三、条款第3至第10条。"
                />
            </View>
            <View style={{ ...styles.row, marginTop: 14, marginBottom: 6 }}>
                <Text style={styles.title}>三、條款</Text>
            </View>
            <View>
                <Text style={styles.terms} fixed>
                    1.甲方 {booking?.member?.name} 确认已收到乙方出借的以上各项设备货品
                    ，且各项设备货品外观、功能皆完整良好，并经乙方说明指导后知悉
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>各项相关操作。</Text>
            </View>

            <View>
                <Table style={{ border: 'none', height: '35pt', marginLeft: -8, marginTop: -8 }}>
                    <TableRow style={{ border: 'none', textAlign: 'left' }}>
                        <TableCell style={{ width: '32%', border: 'none' }}>
                            <Text style={styles.terms}>2.甲方提供身分证原件正反面复印件及</Text>
                        </TableCell>
                        <TableCell style={{ width: '10%', border: 'none' }}>
                            <View style={{ marginLeft: -20, marginTop: 2 }}>
                                <CheckBox
                                    style={{ marginRight: 4, lineHeight: 1.7 }}
                                    check={false}
                                    fontSize={'9pt'}
                                    labelFontSize={'9pt'}
                                    label=" 现金"
                                />
                            </View>
                        </TableCell>
                        <TableCell style={{ width: '10%', border: 'none' }}>
                            <View style={{ marginLeft: -38, marginTop: 2 }}>
                                <CheckBox
                                    style={{ marginRight: 4, lineHeight: 1.7 }}
                                    check={false}
                                    fontSize={'9pt'}
                                    labelFontSize={'9pt'}
                                    label=" 微信"
                                />
                            </View>
                        </TableCell>
                        <TableCell style={{ width: '10%', border: 'none' }}>
                            <View style={{ marginLeft: -56, marginTop: 2 }}>
                                <CheckBox
                                    style={{ marginRight: 4, lineHeight: 1.7 }}
                                    check={false}
                                    fontSize={'9pt'}
                                    labelFontSize={'9pt'}
                                    label=" 支付宝"
                                />
                            </View>
                        </TableCell>
                        <TableCell style={{ width: '10%', border: 'none' }}>
                            <View style={{ marginLeft: -68, marginTop: 2 }}>
                                <CheckBox
                                    style={{ marginRight: 4, lineHeight: 1.7 }}
                                    check={false}
                                    fontSize={'9pt'}
                                    labelFontSize={'9pt'}
                                    label=" 银行卡"
                                />
                            </View>
                        </TableCell>
                        <TableCell style={{ border: 'none' }}>
                            <Text style={{ ...styles.terms, marginLeft: -80 }}>
                                {' '}
                                人民币 __________ 元作为押金使用。
                            </Text>
                        </TableCell>
                    </TableRow>
                </Table>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginTop: -10 }}>
                    3.兹因甲方设备损坏向乙方送修，在维修期间暂借设备替代使用，暂借替代使用以乙方当时现有之设备为主。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    4.设备维修完成后乙方须联络甲方约定取件日期，甲方应依约到门市取回设备，完成维修付款程序，并归还暂借设备。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    5.送修后，如甲方在收到乙方的报价通知后，请于七个工作天内回覆是否维修或另行与乙方约定回覆时间。
                    乙方至多保留该维修件
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    30个工作日（自乙方维修报价日起算）为保留期，但若遇甲方维修零件待料不在此限。但于保留期过后，
                    甲方仍无回覆是否维修或
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    取件，则甲方同意以方有权将所收取之暂借设备押金转为甲方购买暂借设备（无保固期）之货款入帐
                    ，不再归还甲方，押金不足货款
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    时，甲方仍须支付不足之货款，另甲方送修之机器同意由乙方处置，甲方绝无异议。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    6.双方同意在实际归还日期由甲方归还暂借之各项设备，如甲方未于归还日期前归还所有设备货品，同意视同购买暂借设备货品者
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    ，乙方有权将所收取之暂借设备货品之押金转为甲方购买之货款，不再归还甲方，押金不足货款时，甲方仍须支付不足之货款。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    7.甲方须善尽对暂借设备之保管责任，若甲方遗失设备货品，则须按照暂借设备货品牌价 ____________________
                    全额赔偿，甲方同意
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>押金不足货款时，甲方仍须支付不足之货款。</Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    8.于暂借期间，如暂借之设备货品产生非人为损坏，请甲方立即与乙方联络更换暂借设备货品。但如因人为损坏，
                    甲方将负完全之修
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    护赔偿责任并同意修护之费用由所支付之押金底扣，不足则另支付。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    9.甲方须善尽对暂借设备日常保养及维护之责任，办理归还时由乙方确认设备外观、功能
                    皆完整良好无误后，乙方须将押金身份正反面
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    复印件反还甲方，并经双方签名确认。如未经双方确认签名，视同未完成归还程序。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>10.乙方推广活动之限定设备，可免押金暂借。</Text>
            </View>
            <Table style={{ marginTop: 5, marginBottom: 6 }}>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>暂借者(甲方):</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20.4%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            {/* <Text style={{ fontWeight: 'medium' }}>借出者(乙方):</Text> */}
                            <Text>借出者(乙方):</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20.4%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '12%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>门市:</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '14%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '100%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>
                                暂借者(甲方): ____________________
                                已归还上述各项货品，并已收到上述全数押金金额及身份证正反面复印件
                            </Text>
                        </View>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>收货者:</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20.4%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>门市:</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20.4%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '12%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>日期:</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '14%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                </TableRow>
            </Table>
            {!hiddenFooter && (
                <View style={{ margin: '8px 0' }}>
                    <ISOFooter date={docInfo.date} version={docInfo.version} fileCode={docInfo.fileCode} lang={lang} />
                </View>
            )}
        </Page>
    );
};
export default BookingPageCN;
