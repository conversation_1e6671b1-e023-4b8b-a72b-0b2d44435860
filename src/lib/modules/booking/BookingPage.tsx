import { Image, Page, StyleSheet, Text, View, Font } from '@react-pdf/renderer';
import React from 'react';
import { Table, TableCell, TableRow } from '../../components/table';
import { TRADITIONAL_CHINESE } from '../../consts';
import ISOFooter from '../common/ISOFooter';
import { BookingDocData } from './types';
import RentDeviceTable from './BookingItem';
import CheckBox from '../common/CheckBox';
import { EnumLanguage } from '../../consts';

import path from 'path';

Font.register({
    family: TRADITIONAL_CHINESE,
    fonts: [
        {
            src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'bold',
        },
        {
            src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'medium',
        },
        {
            src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'regular',
        },
    ],
});
Font.registerHyphenationCallback((word) => word.split(''));

const tableRows = [0, 1, 2];
const styles = StyleSheet.create({
    page: {
        fontFamily: TRADITIONAL_CHINESE,
        backgroundColor: 'white',
        fontSize: 9,
        padding: '8mm 10mm',
    },
    row: {
        display: 'flex',
        flexDirection: 'row',
    },
    title: {
        fontSize: 11,
        fontWeight: 'bold',
    },
    terms: {
        fontSize: 9,
        lineHeight: '1.5',
    },
});

interface Props {
    companyTitle?: string;
    booking?: BookingDocData;
    hiddenFooter?: boolean;
    lang: EnumLanguage;
}
const BookingPage: React.FC<Props> = (props) => {
    const { companyTitle, booking, hiddenFooter = false, lang } = props;

    return (
        <Page size="A4" style={styles.page}>
            <Text
                style={{ textAlign: 'right', fontSize: '6pt' }}
                fixed
                render={({ pageNumber }) => pageNumber === 1 && `第一聯(門市人員收執)`}
            />
            <Text
                style={{ textAlign: 'right', fontSize: '6pt' }}
                fixed
                render={({ pageNumber }) => pageNumber === 2 && `第二聯(用戶留存收執)`}
            />
            <View fixed>
                <View style={{ textAlign: 'center', marginBottom: '2mm' }}>
                    <Text style={{ fontSize: '22pt', lineHeight: '1.5', fontWeight: 'bold' }}>{companyTitle}</Text>
                    <Text style={{ fontSize: '16pt', lineHeight: '1.5' }}>暫借切結書</Text>
                </View>
            </View>
            <Table>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>用戶姓名(甲方):</Text>
                    </TableCell>
                    <TableCell style={{ width: '48.4%' }}>
                        <Text>{booking?.member?.name}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>身 分 證 字 號:</Text>
                    </TableCell>
                    <TableCell style={{ width: '18.4%' }}>
                        <Text></Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>聯 絡 地 址:</Text>
                    </TableCell>
                    <TableCell style={{ width: '48.4%' }}>
                        <Text>{booking?.member?.address}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>聯 絡 電 話:</Text>
                    </TableCell>
                    <TableCell style={{ width: '18.4%' }}>
                        <Text>{booking?.member?.mobile || booking?.member?.phone}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>借 出 日 期:</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>{booking?.rentStartDate}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>預 計 歸 還 日:</Text>
                    </TableCell>
                    <TableCell style={{ width: '15.2%' }}>
                        <Text>{booking?.rentEndDate}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>實 際 歸 還 日:</Text>
                    </TableCell>
                    <TableCell style={{ width: '18.4%' }}>
                        <Text></Text>
                    </TableCell>
                </TableRow>
            </Table>
            <View style={{ ...styles.row, marginTop: 17, marginBottom: 4 }}>
                <Text style={styles.title}>一、暫借產品品項</Text>
            </View>
            <Table wrap={false}>
                <TableRow>
                    <TableCell style={{ width: '15%' }}>
                        <View>
                            <Text>產品</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '40%' }}>
                        <View>
                            <Text>型號</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>
                        <View>
                            <Text>機器序號</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '10%' }}>
                        <View>
                            <Text>數量</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '15%' }}>
                        <View>
                            <Text>牌價</Text>
                        </View>
                    </TableCell>
                </TableRow>
                {tableRows.map((item, index) => (
                    <RentDeviceTable rentDevice={booking?.rentDevice[index]} key={index} />
                ))}
            </Table>
            <View style={{ ...styles.row, marginTop: 17, marginBottom: 6 }}>
                <Text style={styles.title}>二、暫借目的區別</Text>
            </View>
            <View style={{ width: '100%', display: 'flex', flexDirection: 'row' }}>
                <CheckBox
                    style={{ marginRight: 4, lineHeight: 1.7 }}
                    check={false}
                    fontSize={'12pt'}
                    labelFontSize={'9pt'}
                    label=" 甲方於未購買前須暫借設備體驗，經雙方同意，適用本切結書三、條款第1、2條及第6條至第10條。"
                />
            </View>
            <View style={{ width: '100%', display: 'flex', flexDirection: 'row', marginTop: 5 }}>
                <CheckBox
                    style={{ marginRight: 4, lineHeight: 1.7 }}
                    check={false}
                    fontSize={'12pt'}
                    labelFontSize={'9pt'}
                    label=" 甲方設備送修，經乙方評估後須暫借設備替代使用，經雙方同意，適用本切結書三、條款第3至第10條。"
                />
            </View>
            <View style={{ ...styles.row, marginTop: 18, marginBottom: 6 }}>
                <Text style={styles.title}>三、條款</Text>
            </View>
            <View>
                <Text style={styles.terms} fixed>
                    1.甲方 {booking?.member?.name} 確認已收到乙方出借的以上各項設備貨品
                    ，且各項設備貨品外觀、功能皆完整良好，並經乙方說明指導後知悉
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>各項相關操作。</Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    2.甲方提供身分證正反面影本及現金新台幣 _________________ 元或填寫信用卡授權單作為押金使用。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    3.茲因甲方設備損壞向乙方送修，在維修期間暫借設備替代使用，暫借替代使用以乙方當時現有之設備為主。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    4.設備維修完成後乙方須聯絡甲方約定取件日期，甲方應依約到門市取回設備，完成維修付款程序，並歸還暫借設備。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    5.送修後，如甲方在收到乙方的報價通知後，請於七個工作天內回覆是否維修或另行與乙方約定回覆時間。
                    乙方至多保留該維修件
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    30工作天（自乙方維修報價日起算）為保留期，但若遇甲方維修零件待料不在此限。但於保留期過後，
                    甲方仍無回覆是否維修或取
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    件，則甲方同意以方有權將所收取之暫借設備押金轉為甲方購買暫借設備（無保固期）之貨款入帳
                    ，不再歸還甲方，押金不足貨款
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    時，甲方仍須支付不足之貨款，另甲方送修之機器同意由乙方處置，甲方絕無異議。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    6.雙方同意在實際歸還日期由甲方歸還暫借之各項設備，如甲方未於歸還日期前歸還所有設備貨品，同意視同購買暫借設備貨品者
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    ，乙方有權將所收取之暫借設備貨品之押金轉為甲方購買之貨款，不再歸還甲方，押金不足貨款時，甲方仍須支付不足之貨款。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    7.甲方須善盡對暫借設備之保管責任，若甲方遺失設備貨品，則須按照暫借設備貨品牌價 ____________________
                    全額賠償。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    8.於暫借期間，如暫借之設備貨品產生非人為損壞，請甲方立即與乙方聯絡更換暫借設備貨品。但如因人為損壞，
                    甲方將負完全之修
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    護賠償責任並同意修護之費用由所支付之押金底扣，不足則另支付。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    9.甲方須善盡對暫借設備日常保養及維護之責任，辦理歸還時由乙方確認設備外觀、功能
                    皆完整良好無誤後，乙方須將押金或信用卡
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    授權單及身分證影本歸還甲方，並經雙方簽名確認。如未經雙方確認簽名，視同未完成歸還程序。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>10.乙方推廣活動之限定設備，可免押金暫借。</Text>
            </View>
            <Table style={{ marginTop: 10, marginBottom: 6 }}>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>暫借者(甲方):</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20.4%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            {/* <Text style={{ fontWeight: 'medium' }}>借出者(乙方):</Text> */}
                            <Text>借出者(乙方):</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20.4%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '12%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>門市:</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '14%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '100%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>
                                暫借者(甲方): ____________________
                                已歸還上述各項貨品，並已收到上述全數押金金額及身分證正反面影本
                            </Text>
                        </View>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>收貨者:</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20.4%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>門市:</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20.4%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '12%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>日期:</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '14%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                </TableRow>
            </Table>
            {!hiddenFooter && (
                <View style={{ margin: '8px 0' }}>
                    <ISOFooter
                        date={booking?.docInfo.date ?? ''}
                        version={booking?.docInfo.version ?? ''}
                        fileCode={booking?.docInfo.fileCode ?? ''}
                        lang={lang}
                    />
                </View>
            )}
        </Page>
    );
};
export default BookingPage;
