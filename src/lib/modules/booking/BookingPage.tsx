import { Image, Page, StyleSheet, Text, View, Font } from '@react-pdf/renderer';
import React from 'react';
import { Table, TableCell, TableRow } from '../../components/table';
import { TRADITIONAL_CHINESE } from '../../consts';
import ISOFooter from '../common/ISOFooter';
import { BookingDocData } from './types';
import RentDeviceTable from './BookingItem';
import CheckBox from '../common/CheckBox';
import { EnumLanguage } from '../../consts';

import path from 'path';

Font.register({
    family: TRADITIONAL_CHINESE,
    fonts: [
        {
            src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'bold',
        },
        {
            src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'medium',
        },
        {
            src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'regular',
        },
    ],
});
Font.registerHyphenationCallback((word) => word.split(''));

const tableRows = [0, 1, 2];
const styles = StyleSheet.create({
    page: {
        fontFamily: TRADITIONAL_CHINESE,
        backgroundColor: 'white',
        fontSize: 9,
        padding: '8mm 10mm',
    },
    row: {
        display: 'flex',
        flexDirection: 'row',
    },
    title: {
        fontSize: 11,
        fontWeight: 'bold',
    },
    terms: {
        fontSize: 9,
        lineHeight: '1.5',
    },
});

interface Props {
    companyTitle?: string;
    booking?: BookingDocData;
    hiddenFooter?: boolean;
    lang: EnumLanguage;
}
const BookingPage: React.FC<Props> = (props) => {
    const { companyTitle, booking, hiddenFooter = false, lang } = props;

    return (
        <Page size="A4" style={styles.page}>
            <Text
                style={{ textAlign: 'right', fontSize: '6pt' }}
                fixed
                render={({ pageNumber }) => pageNumber === 1 && `第一聯(門市人員收執)`}
            />
            <Text
                style={{ textAlign: 'right', fontSize: '6pt' }}
                fixed
                render={({ pageNumber }) => pageNumber === 2 && `第二聯(用戶留存收執)`}
            />
            <View fixed>
                <View style={{ textAlign: 'center', marginBottom: '2mm' }}>
                    <Text style={{ fontSize: '22pt', lineHeight: '1.5', fontWeight: 'bold' }}>{companyTitle}</Text>
                    <Text style={{ fontSize: '16pt', lineHeight: '1.5' }}>暫借切結書</Text>
                </View>
            </View>
            <Table>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>用戶姓名(甲方):</Text>
                    </TableCell>
                    <TableCell style={{ width: '31.8%' }}>
                        <Text>{booking?.member?.name}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>會 員 編 號:</Text>
                    </TableCell>
                    <TableCell style={{ width: '35%' }}>
                        <Text>{booking?.member?.memberCode}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>聯 絡 地 址:</Text>
                    </TableCell>
                    <TableCell style={{ width: '48.4%' }}>
                        <Text>{booking?.member?.address}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>聯 絡 電 話:</Text>
                    </TableCell>
                    <TableCell style={{ width: '18.4%' }}>
                        <Text>{booking?.member?.mobile || booking?.member?.phone}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>借 出 日 期:</Text>
                    </TableCell>
                    <TableCell style={{ width: '31.8%' }}>
                        <Text>{booking?.rentStartDate}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>歸 還 日 期:</Text>
                    </TableCell>
                    <TableCell style={{ width: '35%' }}>
                        <Text>{booking?.rentEndDate}</Text>
                    </TableCell>
                </TableRow>
            </Table>
            <View style={{ ...styles.row, marginTop: 17, marginBottom: 4 }}>
                <Text style={styles.title}>一、暫借產品品項</Text>
            </View>
            <Table wrap={false}>
                <TableRow>
                    <TableCell style={{ width: '15%' }}>
                        <View>
                            <Text>產品</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '40%' }}>
                        <View>
                            <Text>型號</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20%' }}>
                        <View>
                            <Text>機器序號</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '10%' }}>
                        <View>
                            <Text>數量</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '15%' }}>
                        <View>
                            <Text>牌價</Text>
                        </View>
                    </TableCell>
                </TableRow>
                {tableRows.map((item, index) => (
                    <RentDeviceTable rentDevice={booking?.rentDevice[index]} key={index} />
                ))}
            </Table>
            <View style={{ ...styles.row, marginTop: 17, marginBottom: 6 }}>
                <Text style={styles.title}>二、暫借目的區分(勾選)</Text>
            </View>
            <View style={{ width: '100%', display: 'flex', flexDirection: 'row' }}>
                <CheckBox
                    style={{ marginRight: 4, lineHeight: 1.7 }}
                    check={false}
                    fontSize={'12pt'}
                    labelFontSize={'9pt'}
                    label=" 甲方於未購買前須暫借設備體驗，經雙方同意，適用本切結書三、條款第1、2條及第6條至第10條。"
                />
            </View>
            <View style={{ width: '100%', display: 'flex', flexDirection: 'row', marginTop: 5 }}>
                <CheckBox
                    style={{ marginRight: 4, lineHeight: 1.7 }}
                    check={false}
                    fontSize={'12pt'}
                    labelFontSize={'9pt'}
                    label=" 甲方設備送修，經乙方評估後須暫借設備替代使用，經雙方同意，適用本切結書三、條款第2條至第10條。"
                />
            </View>
            <View style={{ ...styles.row, marginTop: 18, marginBottom: 6 }}>
                <Text style={styles.title}>三、條款</Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    1.甲方確認已收到乙方出借以上各項設備貨品，並確認其外觀及功能均屬完整良好，且經乙方人員說明指導後，已充分了解相關操作方式。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    2.甲方已提供身份證正反面影本。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    3.因甲方原設備損壞送修，乙方於維修期間出借暫借設備供替代使用，其型號、功能及規格以乙方當時現有設備為準，甲方應予配合使用。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    4.原設備維修完成後，乙方應通知甲方約定取件日期，甲方應依約至乙方借出門市取回設備並完成維修付款程序，同時歸還暫借設備。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    5.甲方於收到乙方報價通知後，應於七個工作天內回覆是否同意維修，或另行與乙方約定回覆期限。乙方得自報價日起保留該送修原設備最長三十
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    個工作天，如因甲方維修項目涉及零件待料，則雙方應另行約定保留期。於保留期過後，甲方仍無回覆是否維修或取件，則甲方同意乙方得視同
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    甲方不進行維修，原設備將另行暫時保管並通知甲方再確認處置方式，必要時得收取保管費用。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    6.雙方同意在上述歸還日期由甲方歸還暫借之各項設備，如甲方未於歸還日期前歸還所有設備貨品，視為遺失，應依本暫借合約書第一條所列之售
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    價賠償。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    7.甲方應善盡對暫借設備之保管義務。如有遺失情形，應依本暫借合約書第一條所列之售價賠償。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    8.暫借期間，如設備發生非人為損壞，甲方應立即通知乙方更換設備。乙方經檢測並合理判定為人為損壞，並提供相關說明後，甲方應負全部修
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    復責任，但以本暫借合約書第一條所列之售價為上限。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    9.甲方應善盡對日常保養及維護之責任，歸還暫借設備時，須經乙方確認暫借設備外觀與功能無誤，並應由雙方簽名確認。如未經雙方簽署確認，
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 9 }}>
                    視為尚未完成歸還。
                </Text>
            </View>
            <View>
                <Text style={styles.terms}>
                    10.本暫借合約書因履行所生之一切爭議，雙方同意以中華民國法律為準據法，並以乙方借出門市所在地地方法院為第一審管轄法院。惟甲方為消費
                </Text>
            </View>
            <View>
                <Text style={{ ...styles.terms, marginLeft: 12 }}>
                    者者，仍得依消費者保護法第47條規定進行。
                </Text>
            </View>
            <Table style={{ marginTop: 10, marginBottom: 6 }}>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>暫借者(甲方):</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20.4%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            {/* <Text style={{ fontWeight: 'medium' }}>借出者(乙方):</Text> */}
                            <Text>借出者(乙方):</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20.4%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '12%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>門市:</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '14%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '100%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>
                                暫借者(甲方): ____________________
                                已歸還上述各項貨品，並已收到上述全數押金金額及身分證正反面影本
                            </Text>
                        </View>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>收貨者:</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20.4%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>門市:</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '20.4%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '12%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text>日期:</Text>
                        </View>
                    </TableCell>
                    <TableCell style={{ width: '14%' }}>
                        <View style={{ height: '5mm', justifyContent: 'center' }}>
                            <Text></Text>
                        </View>
                    </TableCell>
                </TableRow>
            </Table>
            {!hiddenFooter && (
                <View style={{ margin: '8px 0' }}>
                    <ISOFooter
                        date={booking?.docInfo.date ?? ''}
                        version={booking?.docInfo.version ?? ''}
                        fileCode={booking?.docInfo.fileCode ?? ''}
                        lang={lang}
                    />
                </View>
            )}
        </Page>
    );
};
export default BookingPage;
