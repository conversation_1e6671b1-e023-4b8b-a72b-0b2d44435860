export interface PickupDocData {
    companyName: string;
    storeCode: string;
    storeName: string;
    orderCode: string;
    shoplineOrderCode: string;
    memberCode: string;
    memberName: string;
    items: PickupItem[];
    totalAmount: number;
    createdDate: string;
    pickupDate: string;
}
export interface PickupItem {
    seq: number;
    materialCode: string;
    materialName: string;
    categoryName: string;
    qty: number;
    unit: string;
    amount: string;
}
