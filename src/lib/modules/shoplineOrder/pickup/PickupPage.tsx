import React from 'react';
import { Image, Page, StyleSheet, Text, View, Font } from '@react-pdf/renderer';
import numeral from 'numeral';
import { Table, TableCell, TableRow } from '../../../components/table';
import { TRADITIONAL_CHINESE } from '../../../consts';
import { EnumLanguage } from '../../../consts';
import ISOFooter from '../../common/ISOFooter';
import PickupItemTable from './PickupItem';
import { PickupDocData } from './types';
import path from 'path';

Font.register({
    family: TRADITIONAL_CHINESE,
    src: path.join(__dirname, '../../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
});
Font.registerHyphenationCallback((word) => word.split(''));
// Create styles
const styles = StyleSheet.create({
    page: {
        fontFamily: TRADITIONAL_CHINESE,
        fontSize: 8,
        padding: '8mm 10mm',
    },
    row: {
        display: 'flex',
        flexDirection: 'row',
    },
    footer: {
        position: 'absolute',
        bottom: 8,
        margin: '0mm 10mm',
        width: '100%',
    },
});

const docInfo = {
    date: '2023.1.1',
    version: '6',
    fileCode: 'SC-N-4-7-161',
};
interface Props {
    fileTitle: string;
    data?: PickupDocData;
    signature?: string;
    hiddenFooter?: boolean;
    lang: EnumLanguage;
}
const CompletePage: React.FC<Props> = (props) => {
    const { fileTitle, data, signature, hiddenFooter = false, lang } = props;
    return (
        <Page size="A4" style={styles.page}>
            <View fixed>
                <Text
                    style={{ textAlign: 'right' }}
                    render={({ pageNumber, totalPages }) => `(${pageNumber} / ${totalPages})`}
                />
                <View style={{ textAlign: 'center', marginBottom: '2mm' }}>
                    <Text style={{ fontSize: '16pt', lineHeight: '1.5', fontWeight: 700 }}>{fileTitle}簽收單</Text>
                </View>
            </View>
            <Table style={{ fontSize: 10 }}>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>單號</Text>
                    </TableCell>
                    <TableCell style={{ width: '33.4%' }}>
                        <Text>{data?.orderCode}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>商城訂單單號</Text>
                    </TableCell>
                    <TableCell style={{ width: '33.4%' }}>
                        <Text>{data?.shoplineOrderCode}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>客戶姓名</Text>
                    </TableCell>
                    <TableCell style={{ width: '33.4%' }}>
                        <Text>{data?.memberName}</Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>客戶編號</Text>
                    </TableCell>
                    <TableCell style={{ width: '33.4%' }}>
                        <Text>{data?.memberCode}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>取貨門市</Text>
                    </TableCell>
                    <TableCell style={{ width: '33.4%' }}>
                        <Text>
                            {data?.storeName}-{data?.storeCode}
                        </Text>
                    </TableCell>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>訂單建立日期</Text>
                    </TableCell>
                    <TableCell style={{ width: '33.4%' }}>
                        <Text>{data?.createdDate}</Text>
                    </TableCell>
                </TableRow>
                <TableRow>
                    <TableCell style={{ width: '16.6%' }}>
                        <Text>總金額</Text>
                    </TableCell>
                    <TableCell style={{ width: '83.4%' }}>
                        <Text>{data?.totalAmount}</Text>
                    </TableCell>
                </TableRow>
            </Table>
            <View style={{ marginTop: 20 }}>
                <PickupItemTable items={data?.items ?? []} />
            </View>

            <View
                style={{
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'flex-end',
                    marginTop: '2.5mm',
                    fontSize: 10,
                    fontWeight: 800,
                }}
            >
                <View style={{ width: '50%', textAlign: 'left' }}>
                    <Text>茲證明：上述貨物全部收迄，貨物外包裝完好，特此簽收。</Text>
                    <Text>簽收日期：{data?.pickupDate}</Text>
                </View>
            </View>
            <View style={{ ...styles.row, justifyContent: 'flex-end' }}>
                <View
                    style={{
                        ...styles.row,
                        alignContent: 'flex-end',
                        justifyContent: 'flex-start',
                        textAlign: 'left',
                        marginTop: '2.5mm',
                        marginBottom: '2.5mm',
                        width: '50%',
                    }}
                >
                    <Text style={{ marginTop: '2mm', fontSize: 10 }}>簽收人:</Text>

                    <View
                        style={{
                            ...styles.row,
                            minWidth: '45mm',
                            borderBottom: '1px solid black',
                            height: '12mm',
                            justifyContent: 'flex-start',
                            padding: '0 2px 2px',
                        }}
                    >
                        {signature && <Image src={signature} style={{ height: '100%' }} />}
                    </View>
                </View>
            </View>
        </Page>
    );
};
export default CompletePage;
