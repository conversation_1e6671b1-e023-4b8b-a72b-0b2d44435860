import React from 'react';
import { Document } from '@react-pdf/renderer';
import { EnumLanguage } from '../../../consts';
import PickupPage from './PickupPage';
import { PickupDocData } from './types';

interface Props {
    fileTitle: string;
    data?: PickupDocData;
    signature?: string;
    hiddenFooter?: boolean;
    lang: EnumLanguage;
}
const PickupDocument: React.FC<Props> = ({ hiddenFooter = false, ...props }) => {
    const { fileTitle, data, signature, lang = EnumLanguage['zh-tw'] } = props;

    return (
        <Document>
            <>
                <PickupPage
                    fileTitle={fileTitle}
                    data={data}
                    hiddenFooter={hiddenFooter}
                    signature={signature}
                    lang={lang}
                />
            </>
        </Document>
    );
};
export default PickupDocument;
