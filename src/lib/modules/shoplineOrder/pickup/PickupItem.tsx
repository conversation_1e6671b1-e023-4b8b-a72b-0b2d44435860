import React from 'react';
import numeral from 'numeral';
import { StyleSheet, Text, View } from '@react-pdf/renderer';
import { Table, TableCell, TableRow } from '../../../components/table';
import CheckBox from '../../common/CheckBox';
import { PickupItem } from './types';

const styles = StyleSheet.create({
    componentRow: {
        display: 'flex',
        flexDirection: 'row',
    },
    componentCol: {
        padding: '0px 2px',
    },
    underLine: {
        borderTop: '1px solid black',
    },
});

interface Props {
    items?: PickupItem[];
}
const PickupItemTable: React.FC<Props> = (props) => {
    const { items } = props;
    return (
        <Table wrap={false}>
            <TableRow style={{ backgroundColor: '#cccccc', height: 30, fontSize: 10 }}>
                <TableCell style={{ width: '7%' }}>
                    <Text style={{ textAlign: 'center' }}>項次</Text>
                </TableCell>
                <TableCell style={{ width: '19.75%' }}>
                    <Text style={{ textAlign: 'center' }}>名稱</Text>
                </TableCell>
                <TableCell style={{ width: '19.75%' }}>
                    <Text style={{ textAlign: 'center' }}>料號</Text>
                </TableCell>
                <TableCell style={{ width: '19.75%' }}>
                    <Text style={{ textAlign: 'center' }}>類別</Text>
                </TableCell>

                <TableCell style={{ width: '7%' }}>
                    <Text>數量</Text>
                </TableCell>
                <TableCell style={{ width: '7%' }}>
                    <Text>單位</Text>
                </TableCell>
                <TableCell style={{ width: '19.75%' }}>
                    <Text>金額</Text>
                </TableCell>
            </TableRow>
            {items?.map((item, index) => (
                <TableRow style={{ height: 50, fontSize: 10 }} key={item.seq}>
                    <TableCell style={{ width: '7%' }}>
                        <Text>{item?.seq}</Text>
                    </TableCell>
                    <TableCell style={{ width: '19.75%' }}>
                        <Text>{item?.materialName}</Text>
                    </TableCell>
                    <TableCell style={{ width: '19.75%' }}>
                        <Text>{item?.materialCode}</Text>
                    </TableCell>
                    <TableCell style={{ width: '19.75%' }}>
                        <Text>{item?.categoryName}</Text>
                    </TableCell>

                    <TableCell style={{ width: '7%' }}>
                        <Text>{item?.qty}</Text>
                    </TableCell>
                    <TableCell style={{ width: '7%' }}>
                        <Text>{item?.unit}</Text>
                    </TableCell>
                    <TableCell style={{ width: '19.75%' }}>
                        <Text>{item?.amount}</Text>
                    </TableCell>
                </TableRow>
            ))}
        </Table>
    );
};
export default PickupItemTable;
