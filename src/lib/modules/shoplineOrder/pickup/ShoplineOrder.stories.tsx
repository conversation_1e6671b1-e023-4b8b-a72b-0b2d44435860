import React, { useEffect, useState } from 'react';
import { Font, PDFViewer } from '@react-pdf/renderer';
import { ComponentStory, ComponentMeta } from '@storybook/react';
import ShoplineOrderDocument from './PickupDocument';
// import { MockSleepData } from 'src/mocks/sleepData';
import { EnumLanguage } from '../../../consts';

const ShoplineOrderStory: ComponentMeta<typeof ShoplineOrderDocument> = {
    title: 'ShoplineOrder/pickup',
    component: ShoplineOrderDocument,
};
export default ShoplineOrderStory;

const Template: ComponentStory<typeof ShoplineOrderDocument> = (args) => {
    return (
        <PDFViewer width="100%" height="800">
            <ShoplineOrderDocument {...args} />
        </PDFViewer>
    );
};
export const Empty = Template.bind({});
Empty.args = {
    hiddenFooter: false,
    lang: EnumLanguage['zh-tw'],
};
export const Mock = Template.bind({});
Mock.args = {
    hiddenFooter: false,
    // data: MockSleepData,
};
export const MockChina = Template.bind({});
MockChina.args = {
    // data: MockSleepData,
    lang: EnumLanguage['zh-cn'],
};
