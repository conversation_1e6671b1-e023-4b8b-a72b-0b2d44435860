import React from 'react';
import path from 'path';
import { Text, View, StyleSheet, Image, Font } from '@react-pdf/renderer';
import dayjs from 'dayjs';
import { InvoiceInfo } from './types';
import { INVOICE_FONT_FAMILY } from '../../consts';

/* storybook 要查看沒有亂碼的字體要把src路徑只留下檔名即可
    .storybook/ main.js staticDirs 已增加設定assets路徑 
*/
Font.register({
    family: INVOICE_FONT_FAMILY,
    fonts: [
        {
            src: path.join(__dirname, '../../assets/fonts/PMingLiU.ttf'),
        },
    ],
});
Font.registerHyphenationCallback((word) => word.split(''));
// Create styles
const styles = StyleSheet.create({
    invoice: {
        backgroundColor: 'white',
        width: '5.7cm',
        height: '9cm',
        border: '1pt solid #777777',
        fontFamily: INVOICE_FONT_FAMILY,
        fontSize: 8,
    },
    logo: {
        width: '5.7cm',
        height: '9cm',
    },
    topInfo: {
        textAlign: 'center',
        fontSize: 24,
        fontWeight: 'demibold',
    },
    row: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignContent: 'space-between',
    },
});

export interface InvoiceProps {
    invoiceInfo: InvoiceInfo;
}
const Invoice: React.FC<InvoiceProps> = (props) => {
    const { invoiceInfo } = props;
    const date = dayjs(invoiceInfo.createDate);

    const year = date.get('year') - 1911;
    const monthStart = Math.ceil((date.get('month') + 1) / 2) * 2 - 1;
    const datePeriods = `${year}年${monthStart.toString().padStart(2, '0')}-${(monthStart + 1)
        .toString()
        .padStart(2, '0')}月`;
    return (
        <View style={styles.invoice}>
            <View style={{ position: 'absolute', height: '100%', width: '100%' }}>
                <View style={{ margin: 'auto', fontSize: 40, fontWeight: 'bold', color: '#ff5050', opacity: 0.2 }}>
                    <Text>副本</Text>
                </View>
            </View>
            <Image style={{ margin: '8 8' }} src={invoiceInfo.logo} />
            <Text style={{ textAlign: 'center', fontSize: 12, marginBottom: 4 }}>電子發票證明聯</Text>
            <Text style={{ textAlign: 'center', fontSize: 20, fontWeight: 'demibold' }}>{datePeriods}</Text>
            <Text style={{ textAlign: 'center', fontSize: 26, fontWeight: 'demibold' }}>{invoiceInfo.invoiceNo}</Text>
            <View style={{ padding: '2 12' }}>
                <Text style={{ marginBottom: 1 }}>{date.format('YYYY-MM-DD HH:mm:ss')}</Text>
                <View style={styles.row}>
                    <View style={{ width: '50%', padding: 1 }}>
                        <Text>隨機碼 {invoiceInfo.checkCode}</Text>
                    </View>
                    <View style={{ width: '50%', padding: 1 }}>
                        <Text>總計 {invoiceInfo.price}</Text>
                    </View>
                    <View style={{ width: '50%', padding: 1 }}>
                        <Text>賣方 {invoiceInfo.sellerTaxId}</Text>
                    </View>
                    {invoiceInfo.buyerTaxId && (
                        <View style={{ width: '50%', padding: 1 }}>
                            <Text>買方 {invoiceInfo.buyerTaxId}</Text>
                        </View>
                    )}
                </View>
                <View style={styles.row}>
                    <View style={{ width: '100%', padding: '4 0' }}>
                        <Image src={invoiceInfo.barCode} />
                    </View>
                    <View style={{ width: '45%' }}>
                        <Image src={invoiceInfo.leftCode} />
                    </View>
                    <View style={{ flex: 1 }} />
                    <View style={{ width: '45%' }}>
                        <Image src={invoiceInfo.rightCode} />
                    </View>
                </View>
            </View>
        </View>
    );
};
export default Invoice;
