import React, { useEffect, useState } from 'react';
import { Font, PDFViewer } from '@react-pdf/renderer';
import { ComponentMeta, ComponentStory } from '@storybook/react';
import QRCode from 'qrcode';

import { InvoiceDocument } from '../..';
import { MockInvoiceInfo, MockTransactionDetail } from '../../../mocks/invoice';

import { base64Barcode } from '../../../utils/barcode';

import clinicoLogo from '../../assets/logo/ibelive.png';

const InvoiceDocumentStory: ComponentMeta<typeof InvoiceDocument> = {
    title: 'Invoice/Invoice',
    component: InvoiceDocument,
};
export default InvoiceDocumentStory;

const Template: ComponentStory<typeof InvoiceDocument> = (args) => {
    const [barCode, setbarCode] = useState<string>();

    useEffect(() => {
        (async () => {
            setbarCode(await base64Barcode('11012TS951177534162'));
        })();
    }, []);

    const [leftCode, setLeftCode] = useState<string>();
    useEffect(() => {
        (async () => {
            setLeftCode(
                await QRCode.toDataURL(
                    'TS951177531101206416200000000000000740000000091265009znohlmBp5JSHvPcYTvssIw==:**********:3:3:1:115:1:150                                                              ',
                    {
                        margin: 2,
                    }
                )
            );
        })();
    }, []);
    const [rightCode, setRightCode] = useState<string>();
    useEffect(() => {
        (async () => {
            setRightCode(
                await QRCode.toDataURL(
                    '**:1:-35:$1:1:1                                                                                                                                   ',
                    {
                        margin: 2,
                    }
                )
            );
        })();
    }, []);
    if (!(leftCode && barCode && rightCode)) {
        return <div />;
    }
    return (
        <PDFViewer width="100%" height="800">
            <InvoiceDocument
                {...args}
                invoiceInfo={{
                    ...args.invoiceInfo,
                    logo: clinicoLogo,
                    barCode: barCode,
                    leftCode: leftCode,
                    rightCode: rightCode,
                }}
                transactionDetail={args.transactionDetail}
            />
        </PDFViewer>
    );
};
export const Mock = Template.bind({});
Mock.args = {
    invoiceInfo: MockInvoiceInfo,
    transactionDetail: MockTransactionDetail,
};
