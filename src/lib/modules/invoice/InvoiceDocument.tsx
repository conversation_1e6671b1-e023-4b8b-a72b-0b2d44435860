import { Document, Page, StyleSheet, Text, View } from '@react-pdf/renderer';
import React from 'react';

import Invoice from './Invoice';
import Detail from './Detail';
import { InvoiceInfo, TransactionDetail } from './types';

const styles = StyleSheet.create({
    page: {
        backgroundColor: 'white',
        padding: 16,
        flexDirection: 'row',
    },
    item: {
        padding: 4,
    },
});

export interface InvoiceDocumentProps {
    invoiceInfo: InvoiceInfo;
    transactionDetail: TransactionDetail;
}
const InvoiceDocument: React.FC<InvoiceDocumentProps> = (props) => {
    const { invoiceInfo, transactionDetail } = props;
    return (
        <Document>
            <Page size="A4" style={styles.page}>
                <View style={styles.item}>
                    <Invoice invoiceInfo={invoiceInfo} />
                </View>
                <View style={styles.item}>
                    <Detail transaction={transactionDetail} />
                </View>
            </Page>
        </Document>
    );
};
export default InvoiceDocument;
