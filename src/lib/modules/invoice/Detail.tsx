import { StyleSheet, Text, View, Font } from '@react-pdf/renderer';

import numeral from 'numeral';
import path from 'path';
import React from 'react';
import { TRADITIONAL_CHINESE } from '../../consts';
import { TransactionDetailItem, TransactionDetail } from './types';

Font.register({
    family: TRADITIONAL_CHINESE,
    src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
});
Font.registerHyphenationCallback((word) => word.split(''));

const styles = StyleSheet.create({
    detail: {
        backgroundColor: 'white',
        width: '5.7cm',
        border: '1pt solid #777777',
        fontFamily: TRADITIONAL_CHINESE,
        fontSize: 8,
        padding: 8,
    },
    divider: {
        width: '100%',
        height: 1,
        margin: '4 0 6',
        borderBottom: '1 solid #777777',
    },
    tableRow: {
        flexDirection: 'row',
        flexWrap: 'nowrap',
    },
});

interface Props {
    transaction: TransactionDetail;
}
const Detail: React.FC<Props> = (props) => {
    const { transaction } = props;

    const isCompany = !!transaction?.buyerTaxId;
    return (
        <View style={styles.detail}>
            <View style={{ textAlign: 'center', marginBottom: 12 }}>
                <Text style={{ fontSize: 14, marginBottom: 2 }}>{transaction.title}</Text>
                <Text style={{ fontSize: 12 }}>{transaction.subTitle}</Text>
            </View>
            <View>
                <View style={styles.tableRow}>
                    <View style={{ width: 100 }}>
                        <Text>名稱</Text>
                    </View>
                    <View style={{ textAlign: 'right', width: 30 }}>
                        <Text>數量</Text>
                    </View>
                    <View style={{ textAlign: 'right', width: 50 }}>
                        <Text>售價</Text>
                    </View>
                </View>
                <View style={styles.divider} />
                <View style={{ minHeight: 60 }}>
                    {transaction.items?.map((item, index) => (
                        <View key={index} style={styles.tableRow}>
                            <View style={{ width: 100 }}>
                                <Text>{item.name}</Text>
                            </View>
                            <View style={{ textAlign: 'right', width: 30 }}>
                                <Text>{numeral(item.qty).format('0,0')}</Text>
                            </View>
                            <View style={{ textAlign: 'right', width: 50 }}>
                                <Text>{`${numeral(item.price).format('0,0')} ${item.taxType}`} </Text>
                            </View>
                        </View>
                    ))}
                </View>
                <View style={styles.divider} />
                <View>
                    {isCompany ? (
                        <>
                            <Text>{`營業人統編: ${transaction?.sellerTaxId}`}</Text>
                            <View style={styles.divider} />
                            <Text>{`合計金額: ${numeral(transaction?.totalTaxedPrice).format('0,0')}`}</Text>
                            <Text>{`未稅金額: ${numeral(transaction?.totalUntaxedPrice).format('0,0')}`}</Text>
                            <Text>{`稅額: ${transaction?.totalTax}`}</Text>
                            <Text>{`應稅總額: ${numeral(transaction?.totalTaxedPrice).format('0,0')}`}</Text>
                        </>
                    ) : (
                        <>
                            <Text>{`發票金額: ${numeral(transaction?.totalTaxedPrice).format('0,0')}`}</Text>
                            <View style={styles.divider} />
                        </>
                    )}
                </View>
                <View style={styles.divider} />
                {transaction?.address && <Text>{transaction.address}</Text>}
                {transaction?.phone && <Text>TEL: {transaction.phone}</Text>}
                <Text style={{ textAlign: 'center', color: '#ff5050' }}>**此副本發票不可對獎**</Text>
            </View>
        </View>
    );
};
export default Detail;
