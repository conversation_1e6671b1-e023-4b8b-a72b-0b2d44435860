import { Svg, Path, G, Polygon, View, Text } from '@react-pdf/renderer';
import React from 'react';

interface Props extends React.ComponentProps<typeof View> {
    check: boolean;
    label: string;
    fontSize?: string;
    labelFontSize?: string;
}
const CheckBox: React.FC<Props> = (props) => {
    const { check, label, fontSize = '7pt', labelFontSize = '7pt', ...restProps } = props;
    return (
        <View {...restProps} style={{ ...restProps.style, display: 'flex', flexDirection: 'row' }}>
            <Svg style={{ width: fontSize, height: fontSize, marginRight: 2 }} viewBox="0 0 24 24">
                <G fill="black">
                    {check ? (
                        <Path d="M10.041 17l-4.5-4.319 1.395-1.435 3.08 2.937 7.021-7.183 1.422 1.409-8.418 8.591zm-5.041-15c-1.654 0-3 1.346-3 3v14c0 1.654 1.346 3 3 3h14c1.654 0 3-1.346 3-3v-14c0-1.654-1.346-3-3-3h-14zm19 3v14c0 2.761-2.238 5-5 5h-14c-2.762 0-5-2.239-5-5v-14c0-2.761 2.238-5 5-5h14c2.762 0 5 2.239 5 5z" />
                    ) : (
                        <Path d="M5 2c-1.654 0-3 1.346-3 3v14c0 1.654 1.346 3 3 3h14c1.654 0 3-1.346 3-3v-14c0-1.654-1.346-3-3-3h-14zm19 3v14c0 2.761-2.238 5-5 5h-14c-2.762 0-5-2.239-5-5v-14c0-2.761 2.238-5 5-5h14c2.762 0 5 2.239 5 5z" />
                    )}
                </G>
            </Svg>
            <Text style={{ fontSize: labelFontSize }}>{label}</Text>
        </View>
    );
};
export default CheckBox;
