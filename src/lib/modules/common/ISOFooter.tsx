import { StyleSheet, Text, View } from '@react-pdf/renderer';
import React from 'react';
import { EnumLanguage } from '../../consts';

const styles = StyleSheet.create({
    footer: {
        fontSize: 8,
        display: 'flex',
        flexDirection: 'row',
    },
});

interface Props {
    date: string;
    version: number | string;
    fileCode: string;
    lang: EnumLanguage;
}
const ISOFooter: React.FC<Props> = (props) => {
    const { date, version, fileCode, lang = EnumLanguage['zh-tw'] } = props;
    return (
        <View style={styles.footer}>
            <View style={{ flex: 1, textAlign: 'left' }}>
                {lang === EnumLanguage['zh-cn'] && (
                    <>
                        <Text>{`制修订日期: ${date}`}</Text>
                    </>
                )}
                {lang === EnumLanguage['zh-tw'] && (
                    <>
                        <Text>{`制修訂日期: ${date}`}</Text>
                    </>
                )}
            </View>
            <View style={{ flex: 1, textAlign: 'center' }}>
                <Text>{`版次: ${version}`}</Text>
            </View>
            <View style={{ flex: 1, textAlign: 'right' }}>
                {lang === EnumLanguage['zh-cn'] && (
                    <>
                        <Text>{`表单编号: ${fileCode}`}</Text>
                    </>
                )}
                {lang === EnumLanguage['zh-tw'] && (
                    <>
                        <Text>{`文件編號: ${fileCode}`}</Text>
                    </>
                )}
            </View>
        </View>
    );
};
export default ISOFooter;
