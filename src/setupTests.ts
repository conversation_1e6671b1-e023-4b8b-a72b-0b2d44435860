// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import '@testing-library/jest-dom';

import { Font, Path } from '@react-pdf/renderer';
import { TRADITIONAL_CHINESE } from './lib';

Font.register({ family: TRADITIONAL_CHINESE, src: '../public/PMingLiU.ttf' });
Font.registerHyphenationCallback((word) => word.split(''));
