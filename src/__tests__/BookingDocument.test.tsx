import { PDFHelper } from '../lib/PDFHelper';
import { MockBookingData } from 'src/mocks/bookingData';
import BookingDocument from '../lib/modules/booking/BookingDocument';
import { EnumLanguage } from '../lib/consts';

describe('BookingDocument', () => {
    test('Run Traditional Chinese Version', () => {
        const helper = new PDFHelper(BookingDocument, {
            companyTitle: '科林儀器股份有限公司',
            booking: MockBookingData,
            hiddenFooter: false,
            lang: EnumLanguage['zh-tw'],
        });
        expect(helper).toBeDefined();
    });

    test('Run Simplified Chinese Version', () => {
        const helper = new PDFHelper(BookingDocument, {
            companyTitle: '科林仪器股份有限公司',
            booking: MockBookingData,
            hiddenFooter: false,
            lang: EnumLanguage['zh-cn'],
        });
        expect(helper).toBeDefined();
    });
});
