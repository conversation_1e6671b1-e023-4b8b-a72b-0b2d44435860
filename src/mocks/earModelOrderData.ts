import { EarModelOrderDocData } from '../lib/modules/earModel/types';

export const MockEarModelOrderData: EarModelOrderDocData = {
    code: '1235',
    createStore: 'N001-門市',
    createUser: 'L0000-測試猿',
    date: '2022-02-30',
    expectedCompletedDate: '2022-02-30',
    memberAge: '1',
    memberCode: 'HA000000',
    memberName: '客人',
    orderCode: 'AS000000',
    orderType: '類別',
    recipientAddress: '地址',
    recipientStore: 'N002-門市',
    recipientUser: 'L0000-測試猿2',
    leftContent: {
        productionSn: 'SNSNSN',
        earLossLevel: '重',
        250: '10',
        500: '20',
        1000: '30',
        2000: '40',
        4000: '50',
        8000: '60',
        orderType: '類別',
        content: '很多字',
        remark: 'remark',
    },
    processes: [{ name: '過程狀態', date: '2022-01-01 13:00', status: '完成', user: 'L0000-測試猿3' }],
};
