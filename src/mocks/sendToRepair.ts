import { SendToRepairDocData } from 'src/lib';

export const SendToRepairMockData: SendToRepairDocData = {
    orderCode: 'AS303-N01907290018',
    receivedStoreCode: 'N045',
    receivedStoreName: '台中崇德門市',
    receivedUserCode: 'L1101',
    receivedUserName: '鍾昀婷',
    memberName: '林麗芸',
    memberCode: 'HA0089270',
    contactPhone: '092131',
    cellphone: '09123321',
    receivedDate: '2020-01-01',
    deliveryMethod: '客戶親取',
    deliveryAddress: '新北市XXX',
    testFee: 1200,
    qty: 1,
    accessory: '盒子',
    checkingExterior: 'Normal',
    checkingExteriorRemark: '只是髒髒的',
    extendedWarranty: '送修產品符合符合保固，詳情請洽門市同仁。',
    items: [
        {
            materialCode: 'H-1231',
            materialName: 'CR60 MNR PS BL',
            SN: 'HA-2132213213',
            inEarSN: 'HA-2132213213',
            shippingDate: '2019-04-30',
            warrantyDate: '2021-04-30',
            other: '右',
            reasons: [
                { check: true, name: '無功能' },
                { check: false, name: '耗電異常' },
            ],
        },
        {
            materialCode: 'H-1231',
            materialName: 'CR60 MNR PS BL',
            SN: 'HA-2132213213',
            inEarSN: 'HA-2132213213',
            shippingDate: '2019-04-30',
            warrantyDate: '2021-04-30',
            other: '左',
            reasons: [
                { check: true, name: '無功能' },
                { check: false, name: '耗電異常' },
            ],
        },
    ],
};
