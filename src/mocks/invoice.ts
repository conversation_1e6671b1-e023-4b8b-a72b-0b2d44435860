import { InvoiceInfo, TransactionDetail } from '../lib';

export const MockInvoiceInfo: InvoiceInfo = {
    logo: '',
    invoiceNo: 'AB-11223344',
    createDate: new Date(),
    checkCode: '9999',
    price: 1234,
    sellerTaxId: '12345678',
    buyerTaxId: '12345678',
    barCode: '',
    leftCode: '',
    rightCode: '',
};
export const MockTransactionDetail: TransactionDetail = {
    title: '科林助聽器',
    subTitle: '臺北萬華門市',
    sellerTaxId: '12345678',
    buyerTaxId: '87654321',
    totalTax: 10,
    totalTaxedPrice: 10000,
    totalUntaxedPrice: 9990,
    address: '台北市萬華區西園路二段258號1樓' + '台北市萬華區西園路二段258號1樓',
    phone: '(02)2332-9568',
    items: [
        {
            name: 'test',
            price: 666,
            qty: 20,
            taxType: 'TX',
        },
    ],
};
