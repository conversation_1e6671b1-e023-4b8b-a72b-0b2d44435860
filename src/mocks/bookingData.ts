import { BookingDocData } from '../lib/modules/booking/types';

export const MockBookingData: BookingDocData = {
    id: 1,
    member: {
        name: '林科林',
        address: '中和區中和路123號',
        phone: '09123456789',
    },
    rentStartDate: '2022-12-01',
    rentEndDate: '2022-12-05',
    rentDevice: [
        {
            SN: '123456789',
            type: '助聽器',
            name: '$Philip 6853 12V 75W KONAN ANDMOLLER MICROSCOPE LAMP',
            price: '68000',
        },
    ],
    docInfo: {
        date: '2025/07/04',
        version: '3',
        fileCode: 'SC-N-4-7-101',
    },
};
