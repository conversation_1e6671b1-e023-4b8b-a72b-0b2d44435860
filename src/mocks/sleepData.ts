import { SleepTestData } from '../lib/modules/sleepTest/types';

export const MockSleepData: SleepTestData = {
    id: 1,
    breathe: {
        ahi: 1.0, //呼吸中止指数(AHI)
        apnea: 2.8, //阻塞指数(Apnea index)
        uai: 4.3, //无法判定,不清楚(UAI)
        oai: 4.4, //阻塞型中止指数(OAI)
        cai: 6.3, //中枢型中止指数(CAI)
        mai: 3, //混合型中止指数(MAI)
        hyponea: 4, //低通气指数(hyponea Index)
        snore: 20, //打鼾频率(Snore)
    },
    bloodOxygen: {
        odi: 2, //血氧低下指標
        avgBloodOxygen: 96, //平均血氧濃度
        minBloodOxygen: 97, //最低血氧濃度
        baseBloodOxygen: 70, //基線血氧濃度
    },
    pulse: {
        minPulse: 65, //最小脈搏
        maxPulse: 88, //最大脈搏
        avgPulse: 79, //平均脈搏
    },
    member: {
        name: '王大明',
        bmi: 29,
        birthday: '1997/06/20',
        sex: '0',
    },
};
