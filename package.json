{"name": "@clinico/pdf-template", "version": "0.0.20-dev.1", "private": false, "main": "./dist/index.js", "dependencies": {"@react-pdf/renderer": "^3.1.9", "@types/react": "^17.0.20", "numeral": "^2.0.6", "react": "^17.0.2", "typescript": "^4.4.2"}, "publishConfig": {"registry": "http://***********:8081/repository/npm-hosted/", "scope": "@clinico"}, "scripts": {"start": "react-scripts start", "build": "rm -rf dist && tsc --build ./tsconfig.build.json && cp -r ./src/lib/assets dist/assets", "test": "react-scripts test", "lint": "npx eslint \"{src,test}/**/*.ts\" --fix", "gen": "ts-node ./example/generate", "prepack": "npm run build", "storybook": "start-storybook -p 6006", "build-storybook": "build-storybook", "version:dev": "npm version prerelease --preid=dev"}, "devDependencies": {"@storybook/addon-actions": "^6.4.21", "@storybook/addon-essentials": "^6.4.21", "@storybook/addon-interactions": "^6.4.21", "@storybook/addon-links": "^6.4.21", "@storybook/node-logger": "^6.4.21", "@storybook/preset-create-react-app": "^3.2.0", "@storybook/react": "^6.4.21", "@storybook/testing-library": "^0.0.9", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^11.2.7", "@testing-library/user-event": "^12.8.3", "@types/jest": "^26.0.24", "@types/node": "^12.20.24", "@types/numeral": "^2.0.2", "@types/qrcode": "^1.4.1", "@types/react-dom": "^17.0.9", "@types/react-router-dom": "^5.3.2", "babel-loader": "^8.1.0", "canvas": "^2.9.1", "copyfiles": "^2.4.1", "core-js": "^3.21.1", "dayjs": "^1.10.7", "eslint": "^7.21.0", "eslint-config-prettier": "^8.1.0", "eslint-plugin-prettier": "^3.3.1", "husky": "^7.0.4", "jsbarcode": "^3.11.5", "prettier": "^2.2.1", "qrcode": "^1.5.0", "react-dom": "^17.0.0", "react-router-dom": "^6.1.1", "react-scripts": "4.0.3", "ts-node": "^10.2.1"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "eslintConfig": {"overrides": [{"files": ["**/*.stories.*"], "rules": {"import/no-anonymous-default-export": "off"}}]}}