"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var numeral_1 = __importDefault(require("numeral"));
var renderer_1 = require("@react-pdf/renderer");
var consts_1 = require("../../../consts");
var table_1 = require("../../../components/table");
var CheckBox_1 = __importDefault(require("../../common/CheckBox"));
var styles = renderer_1.StyleSheet.create({
    componentRow: {
        display: 'flex',
        flexDirection: 'row',
    },
    componentCol: {
        padding: '0px 2px',
    },
    underLine: {
        borderTop: '1px solid black',
    },
});
var CompleteItemTable = function (props) {
    var item = props.item, lang = props.lang;
    return ((0, jsx_runtime_1.jsxs)(table_1.Table, __assign({ wrap: false }, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15%' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u4EA7\u54C1\u7F16\u53F7" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u7522\u54C1\u7DE8\u865F" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '7mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.materialCode }) }))] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15%' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u4EA7\u54C1\u578B\u53F7" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u7522\u54C1\u578B\u865F" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '7mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.materialName }) }))] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15%' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u4EA7\u54C1\u5E8F\u53F7" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u7522\u54C1\u5E8F\u865F" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '7mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.SN }) }))] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15%' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8033\u5185\u5E8F\u53F7" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8033\u5167\u5E8F\u865F" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '7mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.inEarSN }) }))] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '12.5%' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8D2D\u4E70\u65E5\u671F" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8CFC\u8CB7\u65E5\u671F" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '7mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.shippingDate }) }))] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '12.5%' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u4FDD\u56FA\u5230\u671F" }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '7mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.warrantyDate }) }))] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15%' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5176\u4ED6" }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '7mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.other }) }))] }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '5%' } }, { children: Array.from('送修原因').map(function (char) { return ((0, jsx_runtime_1.jsx)(renderer_1.Text, { children: char }, char)); }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '95%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '13mm', display: 'flex', flexDirection: 'row', flexWrap: 'wrap' } }, { children: item === null || item === void 0 ? void 0 : item.reasons.map(function (reason, index) { return ((0, jsx_runtime_1.jsx)(CheckBox_1.default, { style: { marginRight: 4, marginBottom: 4 }, check: reason.check, label: reason.name }, index)); }) })) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableCell, __assign({ style: { width: '5%' } }, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: Array.from('维修内容').map(function (char) { return ((0, jsx_runtime_1.jsx)(renderer_1.Text, { children: char }, char)); }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: Array.from('維修內容').map(function (char) { return ((0, jsx_runtime_1.jsx)(renderer_1.Text, { children: char }, char)); }) }))] })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '95%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.maintenanceContent }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, __assign({ style: { minHeight: '28mm' } }, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '5%' } }, { children: Array.from('更換零件').map(function (char) { return ((0, jsx_runtime_1.jsx)(renderer_1.Text, { children: char }, char)); }) })), (0, jsx_runtime_1.jsxs)(table_1.TableCell, __assign({ style: { width: '95%', justifyContent: 'flex-start' } }, { children: [(0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: styles.componentRow }, { children: [(0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.componentCol), { width: '15%' }) }, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u54C1\u53F7" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u54C1\u865F" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, { style: __assign({}, styles.underLine) })] })), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.componentCol), { width: '25%' }) }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u54C1\u540D" }), (0, jsx_runtime_1.jsx)(renderer_1.View, { style: __assign({}, styles.underLine) })] })), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.componentCol), { width: '15%', textAlign: 'right' }) }, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u7EF4\u4FEE\u6570\u91CF" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u7DAD\u4FEE\u6578\u91CF" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, { style: __assign({}, styles.underLine) })] })), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.componentCol), { width: '15%', textAlign: 'right' }) }, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u539F\u5E01\u91D1\u989D" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u539F\u5E63\u91D1\u984D" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, { style: __assign({}, styles.underLine) })] })), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.componentCol), { width: '15%', textAlign: 'right' }) }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u4FDD\u56FA\u5167\u6298\u62B5" }), (0, jsx_runtime_1.jsx)(renderer_1.View, { style: __assign({}, styles.underLine) })] })), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.componentCol), { width: '15%', textAlign: 'right' }) }, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5C0F\u8BA1" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5C0F\u8A08" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, { style: __assign({}, styles.underLine) })] }))] })), item === null || item === void 0 ? void 0 : item.repairItems.map(function (repairItem, index) { return ((0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.componentRow), { margin: '1px 0' }) }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.componentCol), { width: '15%' }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: repairItem.code }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.componentCol), { width: '25%' }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: repairItem.name }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.componentCol), { width: '15%', textAlign: 'right' }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (0, numeral_1.default)(repairItem.qty).format('0,0') }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.componentCol), { width: '15%', textAlign: 'right' }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (0, numeral_1.default)(repairItem.estimatedAmount).format('0,0') }) })), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.componentCol), { width: '15%', textAlign: 'right' }) }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { children: repairItem.discount > 0 && (0, numeral_1.default)(repairItem.discount * -1).format('0,0') }), (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: repairItem.discount == 0 && (0, numeral_1.default)(0).format('0,0') })] })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.componentCol), { width: '20%', textAlign: 'right' }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (0, numeral_1.default)(repairItem.price).format('0,0') }) }))] }), index)); })] }))] }))] })));
};
exports.default = CompleteItemTable;
