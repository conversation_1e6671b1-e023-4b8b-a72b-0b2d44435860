"use strict";
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var consts_1 = require("../../../consts");
var CompletePage_1 = __importDefault(require("./CompletePage"));
var CompletePageCN_1 = __importDefault(require("./CompletePageCN"));
var CompleteDocument = function (_a) {
    var _b = _a.hiddenFooter, hiddenFooter = _b === void 0 ? false : _b, props = __rest(_a, ["hiddenFooter"]);
    var fileTitle = props.fileTitle, data = props.data, signature = props.signature, _c = props.lang, lang = _c === void 0 ? consts_1.EnumLanguage['zh-tw'] : _c;
    return ((0, jsx_runtime_1.jsxs)(renderer_1.Document, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(CompletePageCN_1.default, { fileTitle: fileTitle, data: data, hiddenFooter: hiddenFooter, signature: signature, lang: lang }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(CompletePage_1.default, { fileTitle: fileTitle, data: data, hiddenFooter: hiddenFooter, signature: signature, lang: lang }) }))] }));
};
exports.default = CompleteDocument;
