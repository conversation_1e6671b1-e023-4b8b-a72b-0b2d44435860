"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var numeral_1 = __importDefault(require("numeral"));
var table_1 = require("../../../components/table");
var consts_1 = require("../../../consts");
var ISOFooter_1 = __importDefault(require("../../common/ISOFooter"));
var CompleteItem_1 = __importDefault(require("./CompleteItem"));
var path_1 = __importDefault(require("path"));
renderer_1.Font.register({
    family: consts_1.TRADITIONAL_CHINESE,
    src: path_1.default.join(__dirname, '../../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
});
renderer_1.Font.registerHyphenationCallback(function (word) { return word.split(''); });
// Create styles
var styles = renderer_1.StyleSheet.create({
    page: {
        fontFamily: consts_1.TRADITIONAL_CHINESE,
        fontSize: 8,
        padding: '8mm 10mm',
    },
    row: {
        display: 'flex',
        flexDirection: 'row',
    },
    footer: {
        position: 'absolute',
        bottom: 8,
        margin: '0mm 10mm',
        width: '100%',
    },
});
var docInfo = {
    date: '2023.1.1',
    version: '6',
    fileCode: 'SC-N-4-7-161',
};
var CompletePageCN = function (props) {
    var fileTitle = props.fileTitle, data = props.data, signature = props.signature, _a = props.hiddenFooter, hiddenFooter = _a === void 0 ? false : _a, lang = props.lang;
    return ((0, jsx_runtime_1.jsxs)(renderer_1.Page, __assign({ size: "A4", style: styles.page }, { children: [(0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ fixed: true }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { style: { textAlign: 'right' }, render: function (_a) {
                            var pageNumber = _a.pageNumber, totalPages = _a.totalPages;
                            return "(".concat(pageNumber, " / ").concat(totalPages, ")");
                        } }), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: { textAlign: 'center', marginBottom: '2mm' } }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { fontSize: '12pt', lineHeight: '1.5' } }, { children: fileTitle })), (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u7EF4\u4FEE\u8BA2\u5355(\u5B8C\u6210\u8054)" })] }))] })), (0, jsx_runtime_1.jsxs)(table_1.Table, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5355\u53F7" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '33.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.orderCode }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6536\u4EF6\u4EBA\u5458" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '33.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.receivedUserName }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5BA2\u6237\u59D3\u540D" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.memberName }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.8%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5BA2\u6237\u7F16\u53F7" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.memberCode }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8054\u7EDC\u7535\u8BDD" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.8%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.phone }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6536\u4EF6\u65E5\u671F" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '33.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.receivedDate }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5B8C\u6210\u65E5\u671F" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '33.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.finishDate }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6536\u4EF6\u95E8\u5E02" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '33.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.receivedStoreName }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u53D6\u4EF6\u95E8\u5E02" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '33.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.pickupStoreName }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u53D6\u4EF6\u5907\u6CE8" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.deliveryMethod }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.8%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u54C1\u9879\u6570\u91CF" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.qty }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5DE5\u7A0B\u5E08" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.8%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.repairUserName }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u968F\u9644\u914D\u4EF6" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '83.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.accessory }) }))] })] }), data === null || data === void 0 ? void 0 : data.items.map(function (item, index) { return ((0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { margin: '1mm 0' } }, { children: (0, jsx_runtime_1.jsx)(CompleteItem_1.default, { item: item, lang: lang }) }), index)); }), (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { margin: '1mm 0' } }, { children: "\u5907\u6CE8:\u673A\u5668\u672C\u4F53\u7ECF\u7EF4\u4FEE\u6216\u7ECF\u4EA4\u6362\u65B0\u54C1\u540E\u7684\u4E0D\u826F\u54C1\u53CA\u4E0D\u826F\u96F6\u4EF6\u6240\u6709\u6743\u5F52\u672C\u516C\u53F8\u6240\u6709" })), (0, jsx_runtime_1.jsx)(table_1.Table, __assign({ wrap: false }, { children: (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u603B\u91D1\u989D(A)" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (0, numeral_1.default)(data === null || data === void 0 ? void 0 : data.totalAmount).format('0,0') }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.8%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5DF2\u4ED8\u91D1\u989D(\u68C0\u6D4B\u8D39B)" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (0, numeral_1.default)(data === null || data === void 0 ? void 0 : data.receivedAmount).format('0,0') }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5E94\u4ED8\u91D1\u989D(A - B)" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.8%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (0, numeral_1.default)(data === null || data === void 0 ? void 0 : data.remainAmount).format('0,0') }) }))] }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { textAlign: 'right', marginTop: '2.5mm' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u672C\u4EBA\u5DF2\u8BE6\u9605\u300C\u7EF4\u4FEE\u8BA2\u5355(\u5B8C\u6210\u8054)\u300D\u5E76\u540C\u610F\u4EE5\u4E0A\u6240\u9648\u8FF0\u5185\u5BB9\u3002" }) })), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { alignContent: 'flex-start', justifyContent: 'flex-end', marginTop: '2.5mm' }) }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { marginTop: '2mm' } }, { children: "\u5BA2\u6237\u53D6\u4EF6\u7B7E\u540D" })), (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [(0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign({ minWidth: '40mm', borderBottom: '1px solid black', height: '8mm', justifyContent: 'center', padding: '0 2px 2px' }, styles.row) }, { children: signature && (0, jsx_runtime_1.jsx)(renderer_1.Image, { src: signature, style: { height: '100%' } }) })), (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { marginTop: 2, fontSize: '6pt' } }, { children: data === null || data === void 0 ? void 0 : data.extendedWarranty }))] })] })), !hiddenFooter && ((0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: styles.footer, fixed: true }, { children: (0, jsx_runtime_1.jsx)(ISOFooter_1.default, { date: docInfo.date, version: docInfo.version, fileCode: docInfo.fileCode, lang: lang }) })))] })));
};
exports.default = CompletePageCN;
