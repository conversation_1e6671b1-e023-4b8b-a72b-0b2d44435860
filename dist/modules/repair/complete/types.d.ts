export interface CompleteDocData {
    orderCode: string;
    receivedStoreName: string;
    pickupStoreName: string;
    receivedUserName: string;
    repairUserName: string;
    memberName: string;
    memberCode: string;
    phone?: string;
    receivedDate: string;
    finishDate: string;
    deliveryMethod: string;
    qty: number;
    accessory?: string;
    items: CompleteItem[];
    totalAmount: number;
    receivedAmount: number;
    /** 應付金額 */
    remainAmount: number;
    /** 延保資格 */
    extendedWarranty?: string;
}
export interface CompleteItem {
    materialCode: string;
    materialName: string;
    SN?: string;
    inEarSN?: string;
    other?: string;
    shippingDate?: string;
    warrantyDate?: string;
    reasons: CompleteItemReason[];
    /** 是否在保固內 */
    isUnderWarranty: boolean;
    maintenanceContent?: string;
    repairItems: RepairItem[];
}
export interface CompleteItemReason {
    check: boolean;
    name: string;
}
export interface RepairItem {
    code: string;
    name: string;
    qty: number;
    estimatedAmount: number;
    price: number;
    discount: number;
}
