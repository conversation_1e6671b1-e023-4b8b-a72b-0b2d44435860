import React from 'react';
import { EnumLanguage } from '../../../consts';
import { SendToRepairDocData } from './types';
interface Props {
    fileTitle?: string;
    orderCodeBarcode?: string;
    memberCodeBarcode?: string;
    clientWebQRCode?: string;
    signature?: string;
    data?: SendToRepairDocData;
    itemOffset: number;
    hiddenFooter?: boolean;
    lang: EnumLanguage;
}
declare const SendToRepairPage: React.FC<Props>;
export default SendToRepairPage;
