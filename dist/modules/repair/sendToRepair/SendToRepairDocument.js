"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var consts_1 = require("../../../consts");
var SendToRepairPage_1 = __importDefault(require("./SendToRepairPage"));
var SendToRepairPageCN_1 = __importDefault(require("./SendToRepairPageCN"));
var SendToRepairDocument = function (props) {
    var fileTitle = props.fileTitle, orderCodeBarcode = props.orderCodeBarcode, memberCodeBarcode = props.memberCodeBarcode, clientWebQRCode = props.clientWebQRCode, signature = props.signature, data = props.data, hiddenFooter = props.hiddenFooter, _a = props.lang, lang = _a === void 0 ? consts_1.EnumLanguage['zh-tw'] : _a;
    var totalPage = data ? Math.ceil(data.items.length / 2) : 1;
    return ((0, jsx_runtime_1.jsxs)(renderer_1.Document, { children: [lang === consts_1.EnumLanguage['zh-cn'] &&
                new Array(totalPage)
                    .fill(0)
                    .map(function (_, i) { return ((0, jsx_runtime_1.jsx)(SendToRepairPageCN_1.default, { fileTitle: fileTitle, orderCodeBarcode: orderCodeBarcode, memberCodeBarcode: memberCodeBarcode, clientWebQRCode: clientWebQRCode, signature: signature, data: data, itemOffset: i * 2, hiddenFooter: hiddenFooter, lang: lang }, i)); }), lang === consts_1.EnumLanguage['zh-tw'] &&
                new Array(totalPage)
                    .fill(0)
                    .map(function (_, i) { return ((0, jsx_runtime_1.jsx)(SendToRepairPage_1.default, { fileTitle: fileTitle, orderCodeBarcode: orderCodeBarcode, memberCodeBarcode: memberCodeBarcode, clientWebQRCode: clientWebQRCode, signature: signature, data: data, itemOffset: i * 2, hiddenFooter: hiddenFooter, lang: lang }, i)); })] }));
};
exports.default = SendToRepairDocument;
