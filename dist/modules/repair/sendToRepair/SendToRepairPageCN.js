"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var numeral_1 = __importDefault(require("numeral"));
var renderer_1 = require("@react-pdf/renderer");
var consts_1 = require("../../../consts");
var table_1 = require("../../../components/table");
var SendToRepairItem_1 = __importDefault(require("./SendToRepairItem"));
var ISOFooter_1 = __importDefault(require("../../common/ISOFooter"));
var CheckBox_1 = __importDefault(require("../../common/CheckBox"));
var path_1 = __importDefault(require("path"));
renderer_1.Font.register({
    family: consts_1.TRADITIONAL_CHINESE,
    src: path_1.default.join(__dirname, '../../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
});
renderer_1.Font.registerHyphenationCallback(function (word) { return word.split(''); });
// Create styles
var styles = renderer_1.StyleSheet.create({
    page: {
        fontFamily: consts_1.TRADITIONAL_CHINESE,
        fontSize: 8,
        padding: '8mm 10mm',
    },
    row: {
        display: 'flex',
        flexDirection: 'row',
    },
});
var docInfo = {
    date: '2023.1.1',
    version: '6',
    fileCode: 'SC-N-4-7-161',
};
var SendToRepairPageCN = function (props) {
    var _a, _b, _c, _d;
    var fileTitle = props.fileTitle, orderCodeBarcode = props.orderCodeBarcode, memberCodeBarcode = props.memberCodeBarcode, clientWebQRCode = props.clientWebQRCode, signature = props.signature, data = props.data, itemOffset = props.itemOffset, _e = props.hiddenFooter, hiddenFooter = _e === void 0 ? false : _e, lang = props.lang;
    return ((0, jsx_runtime_1.jsxs)(renderer_1.Page, __assign({ size: "A4", style: styles.page }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { style: { textAlign: 'right' }, render: function (_a) {
                    var pageNumber = _a.pageNumber, totalPages = _a.totalPages;
                    return "(".concat(pageNumber, " / ").concat(totalPages, ")");
                }, fixed: true }), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { marginBottom: '2mm' }) }, { children: [(0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: __assign({ flex: 1, alignItems: 'center' }, styles.row) }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { marginRight: 8 } }, { children: "\u5355\u53F7" })), orderCodeBarcode && (0, jsx_runtime_1.jsx)(renderer_1.Image, { src: orderCodeBarcode, style: { height: '10mm' } })] })), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: { flex: 1, textAlign: 'center' } }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { fontSize: '11pt', lineHeight: '1.5' } }, { children: fileTitle })), (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u7EF4\u4FEE\u8BA2\u5355(\u9001\u4FEE\u8054)" })] })), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: __assign(__assign({ flex: 1, alignItems: 'center' }, styles.row), { justifyContent: 'flex-end' }) }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { marginRight: 8 } }, { children: "\u5BA2\u7F16" })), memberCodeBarcode && (0, jsx_runtime_1.jsx)(renderer_1.Image, { src: memberCodeBarcode, style: { height: '10mm' } })] }))] })), (0, jsx_runtime_1.jsxs)(table_1.Table, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '13.3%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5355\u53F7" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20%', paddingTop: 0, paddingBottom: 0 } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.orderCode }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '13.3%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6536\u4EF6\u5355\u4F4D" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20%', paddingTop: 0, paddingBottom: 0 } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data ? "".concat(data.receivedStoreCode, " ").concat(data.receivedStoreName) : '' }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '13.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6536\u4EF6\u4EBA\u5458" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data ? "".concat(data.receivedUserCode, " ").concat(data.receivedUserName) : '' }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '13.3%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5BA2\u6237\u59D3\u540D" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.memberName }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '13.3%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8054\u7EDC\u7535\u8BDD" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.contactPhone }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '13.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6536\u4EF6\u65E5\u671F" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.receivedDate }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '13.3%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5BA2\u6237\u7F16\u53F7" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.memberCode }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '13.3%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u884C\u52A8\u7535\u8BDD" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.cellphone }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '13.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u4EA4\u8D27\u65B9\u5F0F" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.deliveryMethod }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '13.3%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u4EA4\u8D27\u5730\u5740" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '86.7%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.deliveryAddress }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '13.3%', borderRight: 0 } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u68C0\u6D4B\u8D39(A)" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '36.7%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data ? (0, numeral_1.default)(data.testFee).format('0,0') : '' }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '13.3%', borderRight: 0 } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u54C1\u9879\u603B\u6570" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '36.7%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.qty }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '13.3%', borderRight: 0 } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u968F\u9644\u914D\u4EF6" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '86.7%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.accessory }) }))] }), (0, jsx_runtime_1.jsx)(SendToRepairItem_1.default, { item: data === null || data === void 0 ? void 0 : data.items[itemOffset], lang: lang }), (0, jsx_runtime_1.jsx)(SendToRepairItem_1.default, { item: data === null || data === void 0 ? void 0 : data.items[itemOffset + 1], lang: lang }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '14%', justifyContent: 'flex-start' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5916\u89C2\u68C0\u67E5" }) })), (0, jsx_runtime_1.jsxs)(table_1.TableCell, __assign({ style: { width: '43%', justifyContent: 'flex-start' } }, { children: [(0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: { display: 'flex', flexDirection: 'row', marginBottom: 4 } }, { children: [(0, jsx_runtime_1.jsx)(CheckBox_1.default, { style: { marginRight: 4 }, check: (data === null || data === void 0 ? void 0 : data.checkingExterior) === 'Normal', label: "\u6B63\u5E38" }), (0, jsx_runtime_1.jsx)(CheckBox_1.default, { style: { marginRight: 4 }, check: (data === null || data === void 0 ? void 0 : data.checkingExterior) === 'Worn', label: "\u635F\u6BC1(\u5305\u542B\u4EA7\u54C1\u672C\u4F53\u3001\u5916\u58F3\u3001\u914D\u4EF6)" })] })), (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5907\u6CE8: ".concat(data ? data.checkingExteriorRemark : '') })] })), (0, jsx_runtime_1.jsxs)(table_1.TableCell, __assign({ style: { width: '43%', padding: 0, border: '2px solid black' } }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { padding: '2px 4px 2px', borderBottom: '1px solid black' } }, { children: "\u672C\u4EBA\u5179\u786E\u8BA4\u540E\u7B7E\u540D\uFF0C\u5546\u54C1\u5916\u89C2\u5982\u4E0A\u6240\u8F7D" })), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: { padding: '4px 4px' } }, { children: [(0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: __assign({}, styles.row) }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5BA2\u6237\u7B7E\u540D" }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '10mm', padding: '0 2px 2px' } }, { children: signature && (0, jsx_runtime_1.jsx)(renderer_1.Image, { src: signature, style: { height: '100%' } }) }))] })), (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { fontSize: '7pt' } }, { children: data === null || data === void 0 ? void 0 : data.extendedWarranty }))] }))] }))] })] }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { margin: '8px 0' } }, { children: (0, jsx_runtime_1.jsx)(ISOFooter_1.default, { date: docInfo.date, version: docInfo.version, fileCode: docInfo.fileCode, lang: lang }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, { style: { borderTop: '1px solid black', padding: '4px 0' } }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { textAlign: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5BA2\u6237\u53D6\u8D27\u6536\u6267\u8054(\u8BF7\u95E8\u5E02\u76D6\u5E97\u7AE0)" }) })), (0, jsx_runtime_1.jsx)(table_1.Table, { children: (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '10%', borderRight: 0, justifyContent: 'flex-start' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6CE8\u610F\u4E8B\u9879" }) })), (0, jsx_runtime_1.jsxs)(table_1.TableCell, __assign({ style: { width: '70%', borderRight: 0 } }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "1.\u8BE5\u4EA7\u54C1\u7ECF\u7531\u5DE5\u7A0B\u5E08\u68C0\u6D4B\uFF0C\u5982\u53D1\u73B0\u4EA7\u54C1\u6545\u969C\u5C5E\u4E8E\u975E\u4FDD\u56FA\u8303\u56F4\u4E4B\u539F\u56E0\uFF0C\u4F8B\u5982:\u4EBA\u4E3A\u635F\u574F\u3001\u6DB2\u4F53\u5165\u4FB5\u3001\u53D7\u6F6E\u3001\u5929\u707E\u3001\u866B\u5BB3......\u7B49\u56E0\u7D20\u5BFC\u81F4\u7684\u6545\u969C\uFF0C\u6216\u4EA4\u7531\u975E\u539F\u5382\u6388\u6743\u4EE3\u7406\u5546\u670D\u52A1\u6240\u9020\u6210\u7684\u635F\u574F\uFF0C\u8BE5\u60C5\u5F62\u9700\u7531\u7528\u6237\u652F\u4ED8\u7EF4\u4FEE\u8D39\u7528\u3002" }), (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "2.\u7EF4\u4FEE\u62A5\u4EF7\u4E8E\u4E09\u5929\u5185\u56DE\u8986\u662F\u5426\u7EF4\u4FEE\uFF0C\u4EE5\u51CF\u5C11\u60A8\u7B49\u5019\u7684\u65F6\u95F4\u3002" }), (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "3.\u673A\u5668\u672C\u4F53\u7ECF\u7EF4\u4FEE\u6216\u7ECF\u4EA4\u6362\u65B0\u54C1\u540E\u7684\u4E0D\u826F\u54C1\u53CA\u4E0D\u826F\u96F6\u4EF6\u6240\u6709\u6743\u5F52\u672C\u516C\u53F8\u6240\u6709\u3002" }), (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "4.\u672C\u516C\u53F8\u5BA2\u6237\u9001\u4FEE\u54C1\u4FDD\u56FA\u671F\u5916\uFF0C\u7531\u6536\u4EF6\u5355\u4F4D\u4E0E\u5BA2\u6237\u914C\u6536\u68C0\u6D4B\u8D39 ________ \u5143\uFF0C\u7ECF\u62A5\u4EF7\u4E0D\u4FEE\u4EA6\u4E0D\u9000\u68C0\u6D4B\u8D39\u7ECF\u62A5\u4EF7\u540E\u5BA2\u6237\u786E\u5B9A\u652F\u4ED8\u8D39\u7528\u7EF4\u4FEE\uFF0C\u6B64\u68C0\u6D4B\u8D39\u53EF\u62B5\u6263\u7EF4\u4FEE\u8D39\u3002\u975E\u672C\u516C\u53F8\u5BA2\u6237\u6536\u8D39\u6807\u51C6\uFF0C\u53E6\u884C\u516C\u544A\u3002" }), (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "5.\u7EF4\u4FEE\u5B8C\u6210\u4EF6(\u542B\u62A5\u4EF7)\u901A\u77E5\u65E5\u8D77\u7B97\uFF0C\u8BF7\u5C3D\u901F\u53D6\u4EF6\uFF0C\u903E 60 \u65E5\u5185\u672A\u9886\u53D6\uFF0C\u672C\u516C\u53F8\u6055\u4E0D\u8D1F\u4FDD\u7BA1\u8D23\u4EFB\uFF0C\u4E0D\u518D\u53E6\u4F5C\u901A\u77E5\u3002" }), (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "6.\u4EE5\u4E0A\u8D44\u6599\u672C\u516C\u53F8\u7CFB\u4F5C\u4E3A\u552E\u540E\u670D\u52A1\u76EE\u7684\u4E4B\u5EFA\u6863\u3001\u8054\u7CFB\u4E0E\u5BA2\u6237\u6EE1\u610F\u5EA6\u8C03\u67E5\u4F7F\u7528\u3002" })] })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20%' } }, { children: clientWebQRCode && (0, jsx_runtime_1.jsx)(renderer_1.Image, { src: clientWebQRCode }) }))] }) }), (0, jsx_runtime_1.jsxs)(table_1.Table, __assign({ style: { borderWidth: 2 } }, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '18%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5355\u53F7" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '32%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.orderCode }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '18%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6536\u4EF6\u4EBA\u5458\u53CA\u65E5\u671F" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.receivedUserName }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.receivedDate }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '18%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5179\u8BC1\u660E\u6536\u5230\u4E4B\u5BA2\u6237" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '32%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.memberName }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '18%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u968F\u9644\u914D\u4EF6" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '32%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.accessory }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, __assign({ style: { height: '12mm' } }, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '18%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "1:\u4EA7\u54C1\u578B\u53F7" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '32%', paddingTop: 0, paddingBottom: 0 } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_a = data === null || data === void 0 ? void 0 : data.items[itemOffset]) === null || _a === void 0 ? void 0 : _a.materialName }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '18%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "2:\u4EA7\u54C1\u578B\u53F7" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '32%', paddingTop: 0, paddingBottom: 0 } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_b = data === null || data === void 0 ? void 0 : data.items[itemOffset + 1]) === null || _b === void 0 ? void 0 : _b.materialName }) }))] })), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '18%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "1:\u4EA7\u54C1\u5E8F\u53F7" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '32%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_c = data === null || data === void 0 ? void 0 : data.items[itemOffset]) === null || _c === void 0 ? void 0 : _c.SN }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '18%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "2:\u4EA7\u54C1\u5E8F\u53F7" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '32%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_d = data === null || data === void 0 ? void 0 : data.items[itemOffset + 1]) === null || _d === void 0 ? void 0 : _d.SN }) }))] })] })), !hiddenFooter && ((0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { margin: '8px 0' } }, { children: (0, jsx_runtime_1.jsx)(ISOFooter_1.default, { date: docInfo.date, version: docInfo.version, fileCode: docInfo.fileCode, lang: lang }) })))] })));
};
exports.default = SendToRepairPageCN;
