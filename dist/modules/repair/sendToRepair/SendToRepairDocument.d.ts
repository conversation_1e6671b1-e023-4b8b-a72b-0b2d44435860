import React from 'react';
import { EnumLanguage } from '../../../consts';
import { SendToRepairDocData } from './types';
interface Props {
    fileTitle: string;
    orderCodeBarcode?: string;
    memberCodeBarcode?: string;
    clientWebQRCode?: string;
    signature?: string;
    data?: SendToRepairDocData;
    hiddenFooter?: boolean;
    lang: EnumLanguage;
}
declare const SendToRepairDocument: React.FC<Props>;
export default SendToRepairDocument;
