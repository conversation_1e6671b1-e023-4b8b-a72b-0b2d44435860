"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var consts_1 = require("../../../consts");
var renderer_1 = require("@react-pdf/renderer");
var table_1 = require("../../../components/table");
var CheckBox_1 = __importDefault(require("../../common/CheckBox"));
var SendToRepairItemTable = function (props) {
    var item = props.item, lang = props.lang;
    return ((0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15%' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u4EA7\u54C1\u7F16\u53F7" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u7522\u54C1\u7DE8\u865F" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '10mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.materialCode }) }))] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15%' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u4EA7\u54C1\u578B\u53F7" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u7522\u54C1\u578B\u865F" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '10mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.materialName }) }))] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15%' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u4EA7\u54C1\u5E8F\u53F7" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u7522\u54C1\u5E8F\u865F" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '10mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.SN }) }))] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15%' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8033\u5185\u5E8F\u53F7" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8033\u5167\u5E8F\u865F" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '10mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.inEarSN }) }))] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '12.5%' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8D2D\u4E70\u65E5\u671F" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8CFC\u8CB7\u65E5\u671F" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '10mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.shippingDate }) }))] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '12.5%' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u4FDD\u56FA\u5230\u671F" }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '10mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.warrantyDate }) }))] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15%' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5176\u4ED6" }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '10mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.other }) }))] }) }))] }), (0, jsx_runtime_1.jsx)(table_1.TableRow, { children: (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '100%' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { marginBottom: 4 } }, { children: "\u9001\u4FEE\u539F\u56E0" })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '13mm', display: 'flex', flexDirection: 'row', flexWrap: 'wrap' } }, { children: item === null || item === void 0 ? void 0 : item.reasons.map(function (reason, index) { return ((0, jsx_runtime_1.jsx)(CheckBox_1.default, { style: { marginRight: 4, marginBottom: 4 }, check: reason.check, label: reason.name }, index)); }) }))] }) })) })] }));
};
exports.default = SendToRepairItemTable;
