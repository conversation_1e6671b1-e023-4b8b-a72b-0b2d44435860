"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var CheckBox = function (props) {
    var check = props.check, label = props.label, _a = props.fontSize, fontSize = _a === void 0 ? '7pt' : _a, _b = props.labelFontSize, labelFontSize = _b === void 0 ? '7pt' : _b, restProps = __rest(props, ["check", "label", "fontSize", "labelFontSize"]);
    return ((0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({}, restProps, { style: __assign(__assign({}, restProps.style), { display: 'flex', flexDirection: 'row' }) }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Svg, __assign({ style: { width: fontSize, height: fontSize, marginRight: 2 }, viewBox: "0 0 24 24" }, { children: (0, jsx_runtime_1.jsx)(renderer_1.G, __assign({ fill: "black" }, { children: check ? ((0, jsx_runtime_1.jsx)(renderer_1.Path, { d: "M10.041 17l-4.5-4.319 1.395-1.435 3.08 2.937 7.021-7.183 1.422 1.409-8.418 8.591zm-5.041-15c-1.654 0-3 1.346-3 3v14c0 1.654 1.346 3 3 3h14c1.654 0 3-1.346 3-3v-14c0-1.654-1.346-3-3-3h-14zm19 3v14c0 2.761-2.238 5-5 5h-14c-2.762 0-5-2.239-5-5v-14c0-2.761 2.238-5 5-5h14c2.762 0 5 2.239 5 5z" })) : ((0, jsx_runtime_1.jsx)(renderer_1.Path, { d: "M5 2c-1.654 0-3 1.346-3 3v14c0 1.654 1.346 3 3 3h14c1.654 0 3-1.346 3-3v-14c0-1.654-1.346-3-3-3h-14zm19 3v14c0 2.761-2.238 5-5 5h-14c-2.762 0-5-2.239-5-5v-14c0-2.761 2.238-5 5-5h14c2.762 0 5 2.239 5 5z" })) })) })), (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { fontSize: labelFontSize } }, { children: label }))] })));
};
exports.default = CheckBox;
