"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var consts_1 = require("../../consts");
var styles = renderer_1.StyleSheet.create({
    footer: {
        fontSize: 8,
        display: 'flex',
        flexDirection: 'row',
    },
});
var ISOFooter = function (props) {
    var date = props.date, version = props.version, fileCode = props.fileCode, _a = props.lang, lang = _a === void 0 ? consts_1.EnumLanguage['zh-tw'] : _a;
    return ((0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: styles.footer }, { children: [(0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: { flex: 1, textAlign: 'left' } }, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5236\u4FEE\u8BA2\u65E5\u671F: ".concat(date) }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5236\u4FEE\u8A02\u65E5\u671F: ".concat(date) }) }))] })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { flex: 1, textAlign: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u7248\u6B21: ".concat(version) }) })), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: { flex: 1, textAlign: 'right' } }, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8868\u5355\u7F16\u53F7: ".concat(fileCode) }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6587\u4EF6\u7DE8\u865F: ".concat(fileCode) }) }))] }))] })));
};
exports.default = ISOFooter;
