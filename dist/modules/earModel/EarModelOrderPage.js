"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var table_1 = require("../../components/table");
var consts_1 = require("../../consts");
var ISOFooter_1 = __importDefault(require("../common/ISOFooter"));
var EarContentTable_1 = __importDefault(require("./EarContentTable"));
var image_1 = require("./image");
var ProcessTable_1 = __importDefault(require("./ProcessTable"));
var path_1 = __importDefault(require("path"));
renderer_1.Font.register({
    family: consts_1.TRADITIONAL_CHINESE,
    src: path_1.default.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
});
renderer_1.Font.registerHyphenationCallback(function (word) { return word.split(''); });
var styles = renderer_1.StyleSheet.create({
    page: {
        fontFamily: consts_1.TRADITIONAL_CHINESE,
        backgroundColor: 'white',
        fontSize: 10,
        padding: '8mm 10mm',
    },
    content: {},
    row: {
        display: 'flex',
        flexDirection: 'row',
    },
    title: {
        fontSize: 12,
        color: 'rgb(244, 160, 0)',
    },
    remake: {
        fontSize: 16,
        border: 1,
        borderColor: 'rgb(256, 0, 0)',
        color: 'rgb(256, 0, 0)',
        position: 'absolute',
        right: 25,
        top: 25,
    },
    fillField: {
        margin: '0 8px',
        padding: '0 8px',
        borderBottom: '1px solid black',
    },
    footer: {
        position: 'absolute',
        bottom: 8,
        margin: '0mm 10mm',
        width: '100%',
    },
});
var docInfo = {
    date: '2020.11.30',
    version: '1',
    fileCode: 'SC-N-4-7-186',
};
var EarModelOrderPage = function (props) {
    var earModelOrder = props.earModelOrder, lang = props.lang;
    return ((0, jsx_runtime_1.jsxs)(renderer_1.Page, __assign({ size: "A4", style: styles.page }, { children: [(0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: { margin: '-8mm -10mm 4mm' } }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Image, { src: image_1.Base64Header }), (earModelOrder === null || earModelOrder === void 0 ? void 0 : earModelOrder.isRemake) && (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.remake }, { children: "\u91CD\u88FD" }))] })), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: styles.content }, { children: [(0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { marginBottom: 8 }) }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { flex: 1 } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.title }, { children: "\u57FA\u672C\u8CC7\u6599" })) })), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: styles.row }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u55AE\u865F:" }), (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.fillField }, { children: earModelOrder === null || earModelOrder === void 0 ? void 0 : earModelOrder.code })), (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u7BA1\u7406\u7DE8\u865F:" }), (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.fillField }, { children: earModelOrder === null || earModelOrder === void 0 ? void 0 : earModelOrder.orderCode }))] }))] })), (0, jsx_runtime_1.jsxs)(table_1.Table, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8A02\u55AE\u985E\u5225" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '83.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: earModelOrder === null || earModelOrder === void 0 ? void 0 : earModelOrder.orderType }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8A02\u55AE\u65E5\u671F" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '33.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: earModelOrder === null || earModelOrder === void 0 ? void 0 : earModelOrder.date }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u9810\u8A08\u5B8C\u6210\u65E5\u671F" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '33.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: earModelOrder === null || earModelOrder === void 0 ? void 0 : earModelOrder.expectedCompletedDate }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8A02\u55AE\u4F86\u6E90" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '33.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: earModelOrder === null || earModelOrder === void 0 ? void 0 : earModelOrder.createStore }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u958B\u55AE\u4EBA\u54E1" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '33.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: earModelOrder === null || earModelOrder === void 0 ? void 0 : earModelOrder.createUser }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6536\u4EF6\u5730\u5740" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '83.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: earModelOrder === null || earModelOrder === void 0 ? void 0 : earModelOrder.recipientAddress }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6536\u4EF6\u9580\u5E02" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '33.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: earModelOrder === null || earModelOrder === void 0 ? void 0 : earModelOrder.recipientStore }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6536\u4EF6\u4EBA" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '33.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: earModelOrder === null || earModelOrder === void 0 ? void 0 : earModelOrder.recipientUser }) }))] })] }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { marginTop: 8, marginBottom: 8 }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.title }, { children: "\u8A02\u88FD\u5167\u5BB9" })) })), (0, jsx_runtime_1.jsx)(table_1.Table, { children: (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5BA2\u6236\u7DE8\u865F" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.7%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: earModelOrder === null || earModelOrder === void 0 ? void 0 : earModelOrder.memberCode }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5BA2\u6236\u59D3\u540D" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.7%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: earModelOrder === null || earModelOrder === void 0 ? void 0 : earModelOrder.memberName }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5BA2\u6236\u5E74\u9F61" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.8%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: earModelOrder === null || earModelOrder === void 0 ? void 0 : earModelOrder.memberAge }) }))] }) }), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: styles.row }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { width: '50%' } }, { children: (0, jsx_runtime_1.jsx)(EarContentTable_1.default, { ear: "\u5DE6\u8033 LEFT", content: earModelOrder === null || earModelOrder === void 0 ? void 0 : earModelOrder.leftContent, lang: lang }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { width: '50%' } }, { children: (0, jsx_runtime_1.jsx)(EarContentTable_1.default, { ear: "\u53F3\u8033 RIGHT", content: earModelOrder === null || earModelOrder === void 0 ? void 0 : earModelOrder.rightContent, lang: lang }) }))] })), (0, jsx_runtime_1.jsx)(ProcessTable_1.default, { processes: earModelOrder === null || earModelOrder === void 0 ? void 0 : earModelOrder.processes, lang: lang }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { margin: '4px 0', fontSize: 8 } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5099\u8A3B: 1. \u6B64\u8868\u55AE\u586B\u5BEB\u8AAA\u660E\u8ACB\u53C3\u7167\u3010\u53D6\u8033\u578B(\u6A21)\u8FA6\u6CD5\u3011(SC-N-3-7-054)" }) }))] })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: styles.footer, fixed: true }, { children: (0, jsx_runtime_1.jsx)(ISOFooter_1.default, { date: docInfo.date, version: docInfo.version, fileCode: docInfo.fileCode, lang: lang }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { position: 'absolute', right: '4mm', bottom: '22mm', fontSize: 8 } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: Array.from('受理訂製後，請於當日將本訂製單與耳型寄出給秘書收執。').join('\n') }) }))] })));
};
exports.default = EarModelOrderPage;
