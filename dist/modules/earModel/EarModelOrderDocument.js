"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var EarModelOrderPage_1 = __importDefault(require("./EarModelOrderPage"));
var EarModelOrderPageCN_1 = __importDefault(require("./EarModelOrderPageCN"));
var consts_1 = require("../../consts");
var EarModelOrderDocument = function (props) {
    var earModelOrder = props.earModelOrder, _a = props.lang, lang = _a === void 0 ? consts_1.EnumLanguage['zh-tw'] : _a;
    return ((0, jsx_runtime_1.jsxs)(renderer_1.Document, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(EarModelOrderPageCN_1.default, { earModelOrder: earModelOrder, lang: lang }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(EarModelOrderPage_1.default, { earModelOrder: earModelOrder, lang: lang }) }))] }));
};
exports.default = EarModelOrderDocument;
