"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var table_1 = require("../../components/table");
var consts_1 = require("../../consts");
var renderer_1 = require("@react-pdf/renderer");
var ProcessTable = function (_a) {
    var _b = _a.processes, processes = _b === void 0 ? [] : _b, lang = _a.lang;
    if (processes.length > 8) {
        throw Error("EarModelOrderProcess can't be more than 8 items.s");
    }
    return ((0, jsx_runtime_1.jsxs)(table_1.Table, { children: [(0, jsx_runtime_1.jsx)(table_1.TableRow, { children: (0, jsx_runtime_1.jsxs)(table_1.TableCell, __assign({ style: { width: '100%', backgroundColor: '#c5c5c5' } }, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5236\u4F5C\u5DE5\u5E8F\u6D41\u7A0B" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u88FD\u4F5C\u5DE5\u5E8F\u6D41\u7A0B morear Use Only" }) }))] })) }), Array.from({ length: 8 }, function (_, index) {
                var _a, _b, _c, _d;
                return ((0, jsx_runtime_1.jsxs)(table_1.TableRow, __assign({ style: { height: '10mm' } }, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '30%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_a = processes[index]) === null || _a === void 0 ? void 0 : _a.name }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '10%', textAlign: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_b = processes[index]) === null || _b === void 0 ? void 0 : _b.status }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '30%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_c = processes[index]) === null || _c === void 0 ? void 0 : _c.user }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '30%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_d = processes[index]) === null || _d === void 0 ? void 0 : _d.date }) }))] }), index));
            })] }));
};
exports.default = ProcessTable;
