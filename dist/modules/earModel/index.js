"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EarModelOrderDocument = void 0;
var EarModelOrderDocument_1 = require("./EarModelOrderDocument");
Object.defineProperty(exports, "EarModelOrderDocument", { enumerable: true, get: function () { return __importDefault(EarModelOrderDocument_1).default; } });
