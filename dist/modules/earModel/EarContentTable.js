"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var consts_1 = require("../../consts");
var table_1 = require("../../components/table");
var EarContentTable = function (props) {
    var ear = props.ear, content = props.content, lang = props.lang;
    return ((0, jsx_runtime_1.jsxs)(table_1.Table, __assign({ style: { height: '80mm' } }, { children: [(0, jsx_runtime_1.jsx)(table_1.TableRow, { children: (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '100%', backgroundColor: '#c5c5c5' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { textAlign: 'center' } }, { children: ear })) })) }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableCell, __assign({ dense: true, style: { width: '33.3%' } }, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5236\u4F5C\u7F16\u53F7" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u88FD\u4F5C\u7DE8\u865F" }) }))] })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '66.7%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: content === null || content === void 0 ? void 0 : content.productionSn }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, __assign({ style: { textAlign: 'center', height: '10mm' } }, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableCell, __assign({ dense: true, style: { width: '16%' } }, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: '听损\n程度' }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: '聽損\n程度' }) }))] })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '14%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "250" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '14%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "500" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '14%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "1K" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '14%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "2K" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '14%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "4K" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '14%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "8K" }) }))] })), (0, jsx_runtime_1.jsxs)(table_1.TableRow, __assign({ style: { textAlign: 'center', height: '10mm' } }, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '16%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: content === null || content === void 0 ? void 0 : content.earLossLevel }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '14%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: content === null || content === void 0 ? void 0 : content[250] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '14%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: content === null || content === void 0 ? void 0 : content[500] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '14%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: content === null || content === void 0 ? void 0 : content[1000] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '14%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: content === null || content === void 0 ? void 0 : content[2000] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '14%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: content === null || content === void 0 ? void 0 : content[4000] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '14%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: content === null || content === void 0 ? void 0 : content[8000] }) }))] })), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableCell, __assign({ dense: true, style: { width: '33.3%' } }, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8BA2\u5355\u7C7B\u522B" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8A02\u55AE\u985E\u5225" }) }))] })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '66.7%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: content === null || content === void 0 ? void 0 : content.orderType }) }))] }), (0, jsx_runtime_1.jsx)(table_1.TableRow, __assign({ style: { height: '30mm' } }, { children: (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ dense: true, style: { width: '100%', justifyContent: 'flex-start' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: content === null || content === void 0 ? void 0 : content.content }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableRow, __assign({ style: { height: '25mm' } }, { children: (0, jsx_runtime_1.jsxs)(table_1.TableCell, __assign({ dense: true, style: { width: '100%', justifyContent: 'flex-start' } }, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5907\u6CE8" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5099\u8A3B" }) })), (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: content === null || content === void 0 ? void 0 : content.remark })] })) }))] })));
};
exports.default = EarContentTable;
