"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var Invoice_1 = __importDefault(require("./Invoice"));
var Detail_1 = __importDefault(require("./Detail"));
var styles = renderer_1.StyleSheet.create({
    page: {
        backgroundColor: 'white',
        padding: 16,
        flexDirection: 'row',
    },
    item: {
        padding: 4,
    },
});
var InvoiceDocument = function (props) {
    var invoiceInfo = props.invoiceInfo, transactionDetail = props.transactionDetail;
    return ((0, jsx_runtime_1.jsx)(renderer_1.Document, { children: (0, jsx_runtime_1.jsxs)(renderer_1.Page, __assign({ size: "A4", style: styles.page }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: styles.item }, { children: (0, jsx_runtime_1.jsx)(Invoice_1.default, { invoiceInfo: invoiceInfo }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: styles.item }, { children: (0, jsx_runtime_1.jsx)(Detail_1.default, { transaction: transactionDetail }) }))] })) }));
};
exports.default = InvoiceDocument;
