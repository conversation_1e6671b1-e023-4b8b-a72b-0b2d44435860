"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var numeral_1 = __importDefault(require("numeral"));
var path_1 = __importDefault(require("path"));
var consts_1 = require("../../consts");
renderer_1.Font.register({
    family: consts_1.TRADITIONAL_CHINESE,
    src: path_1.default.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
});
renderer_1.Font.registerHyphenationCallback(function (word) { return word.split(''); });
var styles = renderer_1.StyleSheet.create({
    detail: {
        backgroundColor: 'white',
        width: '5.7cm',
        border: '1pt solid #777777',
        fontFamily: consts_1.TRADITIONAL_CHINESE,
        fontSize: 8,
        padding: 8,
    },
    divider: {
        width: '100%',
        height: 1,
        margin: '4 0 6',
        borderBottom: '1 solid #777777',
    },
    tableRow: {
        flexDirection: 'row',
        flexWrap: 'nowrap',
    },
});
var Detail = function (props) {
    var _a;
    var transaction = props.transaction;
    var isCompany = !!(transaction === null || transaction === void 0 ? void 0 : transaction.buyerTaxId);
    return ((0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: styles.detail }, { children: [(0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: { textAlign: 'center', marginBottom: 12 } }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { fontSize: 14, marginBottom: 2 } }, { children: transaction.title })), (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { fontSize: 12 } }, { children: transaction.subTitle }))] })), (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [(0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: styles.tableRow }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { width: 100 } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u540D\u7A31" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { textAlign: 'right', width: 30 } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6578\u91CF" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { textAlign: 'right', width: 50 } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u552E\u50F9" }) }))] })), (0, jsx_runtime_1.jsx)(renderer_1.View, { style: styles.divider }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { minHeight: 60 } }, { children: (_a = transaction.items) === null || _a === void 0 ? void 0 : _a.map(function (item, index) { return ((0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: styles.tableRow }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { width: 100 } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item.name }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { textAlign: 'right', width: 30 } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (0, numeral_1.default)(item.qty).format('0,0') }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { textAlign: 'right', width: 50 } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.Text, { children: ["".concat((0, numeral_1.default)(item.price).format('0,0'), " ").concat(item.taxType), " "] }) }))] }), index)); }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, { style: styles.divider }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: isCompany ? ((0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u71DF\u696D\u4EBA\u7D71\u7DE8: ".concat(transaction === null || transaction === void 0 ? void 0 : transaction.sellerTaxId) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { style: styles.divider }), (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5408\u8A08\u91D1\u984D: ".concat((0, numeral_1.default)(transaction === null || transaction === void 0 ? void 0 : transaction.totalTaxedPrice).format('0,0')) }), (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u672A\u7A05\u91D1\u984D: ".concat((0, numeral_1.default)(transaction === null || transaction === void 0 ? void 0 : transaction.totalUntaxedPrice).format('0,0')) }), (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u7A05\u984D: ".concat(transaction === null || transaction === void 0 ? void 0 : transaction.totalTax) }), (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u61C9\u7A05\u7E3D\u984D: ".concat((0, numeral_1.default)(transaction === null || transaction === void 0 ? void 0 : transaction.totalTaxedPrice).format('0,0')) })] })) : ((0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u767C\u7968\u91D1\u984D: ".concat((0, numeral_1.default)(transaction === null || transaction === void 0 ? void 0 : transaction.totalTaxedPrice).format('0,0')) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { style: styles.divider })] })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { style: styles.divider }), (transaction === null || transaction === void 0 ? void 0 : transaction.address) && (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: transaction.address }), (transaction === null || transaction === void 0 ? void 0 : transaction.phone) && (0, jsx_runtime_1.jsxs)(renderer_1.Text, { children: ["TEL: ", transaction.phone] }), (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { textAlign: 'center', color: '#ff5050' } }, { children: "**\u6B64\u526F\u672C\u767C\u7968\u4E0D\u53EF\u5C0D\u734E**" }))] })] })));
};
exports.default = Detail;
