"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var path_1 = __importDefault(require("path"));
var renderer_1 = require("@react-pdf/renderer");
var dayjs_1 = __importDefault(require("dayjs"));
var consts_1 = require("../../consts");
/* storybook 要查看沒有亂碼的字體要把src路徑只留下檔名即可
    .storybook/ main.js staticDirs 已增加設定assets路徑
*/
renderer_1.Font.register({
    family: consts_1.INVOICE_FONT_FAMILY,
    fonts: [
        {
            src: path_1.default.join(__dirname, '../../assets/fonts/PMingLiU.ttf'),
        },
    ],
});
renderer_1.Font.registerHyphenationCallback(function (word) { return word.split(''); });
// Create styles
var styles = renderer_1.StyleSheet.create({
    invoice: {
        backgroundColor: 'white',
        width: '5.7cm',
        height: '9cm',
        border: '1pt solid #777777',
        fontFamily: consts_1.INVOICE_FONT_FAMILY,
        fontSize: 8,
    },
    logo: {
        width: '5.7cm',
        height: '9cm',
    },
    topInfo: {
        textAlign: 'center',
        fontSize: 24,
        fontWeight: 'demibold',
    },
    row: {
        flexDirection: 'row',
        flexWrap: 'wrap',
        alignContent: 'space-between',
    },
});
var Invoice = function (props) {
    var invoiceInfo = props.invoiceInfo;
    var date = (0, dayjs_1.default)(invoiceInfo.createDate);
    var year = date.get('year') - 1911;
    var monthStart = Math.ceil((date.get('month') + 1) / 2) * 2 - 1;
    var datePeriods = "".concat(year, "\u5E74").concat(monthStart.toString().padStart(2, '0'), "-").concat((monthStart + 1)
        .toString()
        .padStart(2, '0'), "\u6708");
    return ((0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: styles.invoice }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { position: 'absolute', height: '100%', width: '100%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { margin: 'auto', fontSize: 40, fontWeight: 'bold', color: '#ff5050', opacity: 0.2 } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u526F\u672C" }) })) })), (0, jsx_runtime_1.jsx)(renderer_1.Image, { style: { margin: '8 8' }, src: invoiceInfo.logo }), (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { textAlign: 'center', fontSize: 12, marginBottom: 4 } }, { children: "\u96FB\u5B50\u767C\u7968\u8B49\u660E\u806F" })), (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { textAlign: 'center', fontSize: 20, fontWeight: 'demibold' } }, { children: datePeriods })), (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { textAlign: 'center', fontSize: 26, fontWeight: 'demibold' } }, { children: invoiceInfo.invoiceNo })), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: { padding: '2 12' } }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { marginBottom: 1 } }, { children: date.format('YYYY-MM-DD HH:mm:ss') })), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: styles.row }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { width: '50%', padding: 1 } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.Text, { children: ["\u96A8\u6A5F\u78BC ", invoiceInfo.checkCode] }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { width: '50%', padding: 1 } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.Text, { children: ["\u7E3D\u8A08 ", invoiceInfo.price] }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { width: '50%', padding: 1 } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.Text, { children: ["\u8CE3\u65B9 ", invoiceInfo.sellerTaxId] }) })), invoiceInfo.buyerTaxId && ((0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { width: '50%', padding: 1 } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.Text, { children: ["\u8CB7\u65B9 ", invoiceInfo.buyerTaxId] }) })))] })), (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: styles.row }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { width: '100%', padding: '4 0' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Image, { src: invoiceInfo.barCode }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { width: '45%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Image, { src: invoiceInfo.leftCode }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, { style: { flex: 1 } }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { width: '45%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Image, { src: invoiceInfo.rightCode }) }))] }))] }))] })));
};
exports.default = Invoice;
