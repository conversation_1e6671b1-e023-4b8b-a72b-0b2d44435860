export interface InvoiceInfo {
    logo: string;
    barCode: string;
    leftCode: string;
    rightCode: string;
    invoiceNo: string;
    createDate: Date;
    /** 隨機碼 */
    checkCode: string;
    price: string | number;
    sellerTaxId: string;
    buyerTaxId?: string;
}
export interface TransactionDetail {
    title: string;
    subTitle?: string;
    sellerTaxId: string;
    buyerTaxId?: string;
    totalTaxedPrice: number;
    totalUntaxedPrice: number;
    totalTax: number;
    address?: string;
    phone?: string;
    items: TransactionDetailItem[];
}
export interface TransactionDetailItem {
    name: string;
    qty: number;
    price: number;
    taxType: 'TX';
}
