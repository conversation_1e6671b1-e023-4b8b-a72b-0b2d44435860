"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var table_1 = require("../../../components/table");
var styles = renderer_1.StyleSheet.create({
    componentRow: {
        display: 'flex',
        flexDirection: 'row',
    },
    componentCol: {
        padding: '0px 2px',
    },
    underLine: {
        borderTop: '1px solid black',
    },
});
var PickupItemTable = function (props) {
    var items = props.items;
    return ((0, jsx_runtime_1.jsxs)(table_1.Table, __assign({ wrap: false }, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableRow, __assign({ style: { backgroundColor: '#cccccc', height: 30, fontSize: 10 } }, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '7%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { textAlign: 'center' } }, { children: "\u9805\u6B21" })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '19.75%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { textAlign: 'center' } }, { children: "\u540D\u7A31" })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '19.75%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { textAlign: 'center' } }, { children: "\u6599\u865F" })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '19.75%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { textAlign: 'center' } }, { children: "\u985E\u5225" })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '7%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6578\u91CF" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '7%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u55AE\u4F4D" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '19.75%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u91D1\u984D" }) }))] })), items === null || items === void 0 ? void 0 : items.map(function (item, index) { return ((0, jsx_runtime_1.jsxs)(table_1.TableRow, __assign({ style: { height: 50, fontSize: 10 } }, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '7%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.seq }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '19.75%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.materialName }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '19.75%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.materialCode }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '19.75%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.categoryName }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '7%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.qty }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '7%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.unit }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '19.75%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: item === null || item === void 0 ? void 0 : item.amount }) }))] }), item.seq)); })] })));
};
exports.default = PickupItemTable;
