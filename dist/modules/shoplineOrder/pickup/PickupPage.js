"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var table_1 = require("../../../components/table");
var consts_1 = require("../../../consts");
var PickupItem_1 = __importDefault(require("./PickupItem"));
var path_1 = __importDefault(require("path"));
renderer_1.Font.register({
    family: consts_1.TRADITIONAL_CHINESE,
    src: path_1.default.join(__dirname, '../../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
});
renderer_1.Font.registerHyphenationCallback(function (word) { return word.split(''); });
// Create styles
var styles = renderer_1.StyleSheet.create({
    page: {
        fontFamily: consts_1.TRADITIONAL_CHINESE,
        fontSize: 8,
        padding: '8mm 10mm',
    },
    row: {
        display: 'flex',
        flexDirection: 'row',
    },
    footer: {
        position: 'absolute',
        bottom: 8,
        margin: '0mm 10mm',
        width: '100%',
    },
});
var docInfo = {
    date: '2023.1.1',
    version: '6',
    fileCode: 'SC-N-4-7-161',
};
var CompletePage = function (props) {
    var _a;
    var fileTitle = props.fileTitle, data = props.data, signature = props.signature, _b = props.hiddenFooter, hiddenFooter = _b === void 0 ? false : _b, lang = props.lang;
    return ((0, jsx_runtime_1.jsxs)(renderer_1.Page, __assign({ size: "A4", style: styles.page }, { children: [(0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ fixed: true }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { style: { textAlign: 'right' }, render: function (_a) {
                            var pageNumber = _a.pageNumber, totalPages = _a.totalPages;
                            return "(".concat(pageNumber, " / ").concat(totalPages, ")");
                        } }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { textAlign: 'center', marginBottom: '2mm' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.Text, __assign({ style: { fontSize: '16pt', lineHeight: '1.5', fontWeight: 700 } }, { children: [fileTitle, "\u7C3D\u6536\u55AE"] })) }))] })), (0, jsx_runtime_1.jsxs)(table_1.Table, __assign({ style: { fontSize: 10 } }, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u55AE\u865F" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '33.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.orderCode }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5546\u57CE\u8A02\u55AE\u55AE\u865F" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '33.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.shoplineOrderCode }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5BA2\u6236\u59D3\u540D" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '33.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.memberName }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5BA2\u6236\u7DE8\u865F" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '33.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.memberCode }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u53D6\u8CA8\u9580\u5E02" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '33.4%' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.Text, { children: [data === null || data === void 0 ? void 0 : data.storeName, "-", data === null || data === void 0 ? void 0 : data.storeCode] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8A02\u55AE\u5EFA\u7ACB\u65E5\u671F" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '33.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.createdDate }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u7E3D\u91D1\u984D" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '83.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data === null || data === void 0 ? void 0 : data.totalAmount }) }))] })] })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { marginTop: 20 } }, { children: (0, jsx_runtime_1.jsx)(PickupItem_1.default, { items: (_a = data === null || data === void 0 ? void 0 : data.items) !== null && _a !== void 0 ? _a : [] }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: {
                    display: 'flex',
                    flexDirection: 'row',
                    justifyContent: 'flex-end',
                    marginTop: '2.5mm',
                    fontSize: 10,
                    fontWeight: 800,
                } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: { width: '50%', textAlign: 'left' } }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8332\u8B49\u660E\uFF1A\u4E0A\u8FF0\u8CA8\u7269\u5168\u90E8\u6536\u8FC4\uFF0C\u8CA8\u7269\u5916\u5305\u88DD\u5B8C\u597D\uFF0C\u7279\u6B64\u7C3D\u6536\u3002" }), (0, jsx_runtime_1.jsxs)(renderer_1.Text, { children: ["\u7C3D\u6536\u65E5\u671F\uFF1A", data === null || data === void 0 ? void 0 : data.pickupDate] })] })) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { justifyContent: 'flex-end' }) }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { alignContent: 'flex-end', justifyContent: 'flex-start', textAlign: 'left', marginTop: '2.5mm', marginBottom: '2.5mm', width: '50%' }) }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { marginTop: '2mm', fontSize: 10 } }, { children: "\u7C3D\u6536\u4EBA:" })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { minWidth: '45mm', borderBottom: '1px solid black', height: '12mm', justifyContent: 'flex-start', padding: '0 2px 2px' }) }, { children: signature && (0, jsx_runtime_1.jsx)(renderer_1.Image, { src: signature, style: { height: '100%' } }) }))] })) }))] })));
};
exports.default = CompletePage;
