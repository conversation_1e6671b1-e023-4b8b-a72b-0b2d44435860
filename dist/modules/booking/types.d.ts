export interface BookingDocData {
    id?: number;
    member: IMember;
    rentStartDate: string;
    rentEndDate: string;
    rentDevice: IRentDevice[];
    docInfo: IDocInfo;
}
export interface IMember {
    name: string;
    address: string;
    phone?: string;
    mobile?: string;
}
export interface IRentDevice {
    type: string;
    name: string;
    SN: string;
    price: string;
}
export interface IDocInfo {
    date: string;
    version: string;
    fileCode: string;
}
