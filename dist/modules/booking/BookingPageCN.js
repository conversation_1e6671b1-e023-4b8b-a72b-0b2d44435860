"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var table_1 = require("../../components/table");
var consts_1 = require("../../consts");
var ISOFooter_1 = __importDefault(require("../common/ISOFooter"));
var BookingItem_1 = __importDefault(require("./BookingItem"));
var CheckBox_1 = __importDefault(require("../common/CheckBox"));
var path_1 = __importDefault(require("path"));
renderer_1.Font.register({
    family: consts_1.TRADITIONAL_CHINESE,
    fonts: [
        {
            src: path_1.default.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'bold',
        },
        {
            src: path_1.default.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'medium',
        },
        {
            src: path_1.default.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'regular',
        },
    ],
});
renderer_1.Font.registerHyphenationCallback(function (word) { return word.split(''); });
var tableRows = [0, 1, 2];
var styles = renderer_1.StyleSheet.create({
    page: {
        fontFamily: consts_1.TRADITIONAL_CHINESE,
        backgroundColor: 'white',
        fontSize: 9,
        padding: '8mm 10mm',
    },
    row: {
        display: 'flex',
        flexDirection: 'row',
    },
    title: {
        fontSize: 11,
        fontWeight: 'bold',
    },
    terms: {
        fontSize: 9,
        lineHeight: '1.5',
    },
});
// TODO ISO文件審核的版本號,等待審核完畢要回來更新
var docInfo = {
    date: '2023-02-28',
    version: '1',
    fileCode: 'C-SC-N-4-7-036',
};
var BookingPageCN = function (props) {
    var _a, _b, _c, _d, _e;
    var companyTitle = props.companyTitle, booking = props.booking, _f = props.hiddenFooter, hiddenFooter = _f === void 0 ? false : _f, lang = props.lang;
    return ((0, jsx_runtime_1.jsxs)(renderer_1.Page, __assign({ size: "A4", style: styles.page }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { style: { textAlign: 'right', fontSize: '6pt' }, fixed: true, render: function (_a) {
                    var pageNumber = _a.pageNumber;
                    return pageNumber === 1 && "\u7B2C\u4E00\u8054(\u95E8\u5E02\u4EBA\u5458\u6536\u6267)";
                } }), (0, jsx_runtime_1.jsx)(renderer_1.Text, { style: { textAlign: 'right', fontSize: '6pt' }, fixed: true, render: function (_a) {
                    var pageNumber = _a.pageNumber;
                    return pageNumber === 2 && "\u7B2C\u4E8C\u8054(\u7528\u6237\u7559\u5B58\u6536\u6267)";
                } }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ fixed: true }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { textAlign: 'center', marginBottom: '2mm' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { fontSize: '20pt', lineHeight: '1.5', fontWeight: 'bold' } }, { children: "\u6682\u501F\u5207\u7ED3\u4E66" })) })) })), (0, jsx_runtime_1.jsxs)(table_1.Table, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u7528\u6237\u59D3\u540D(\u7532\u65B9):" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '48.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_a = booking === null || booking === void 0 ? void 0 : booking.member) === null || _a === void 0 ? void 0 : _a.name }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8EAB \u4EFD \u8BC1 \u53F7:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '18.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8054 \u7EDC \u5730 \u5740:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '48.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_b = booking === null || booking === void 0 ? void 0 : booking.member) === null || _b === void 0 ? void 0 : _b.address }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8054 \u7EDC \u7535 \u8BDD:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '18.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: ((_c = booking === null || booking === void 0 ? void 0 : booking.member) === null || _c === void 0 ? void 0 : _c.mobile) || ((_d = booking === null || booking === void 0 ? void 0 : booking.member) === null || _d === void 0 ? void 0 : _d.phone) }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u501F \u51FA \u65E5 \u671F:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: booking === null || booking === void 0 ? void 0 : booking.rentStartDate }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u9884 \u8BA1 \u5F52 \u8FD8 \u65E5:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15.2%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: booking === null || booking === void 0 ? void 0 : booking.rentEndDate }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5B9E \u9645 \u5F52 \u8FD8 \u65E5:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '18.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) }))] })] }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { marginTop: 16, marginBottom: 4 }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.title }, { children: "\u4E00\u3001\u6682\u501F\u4EA7\u54C1\u54C1\u9879" })) })), (0, jsx_runtime_1.jsxs)(table_1.Table, __assign({ wrap: false }, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u4EA7\u54C1" }) }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '40%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u578B\u53F7" }) }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u673A\u5668\u5E8F\u53F7" }) }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '10%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6570\u91CF" }) }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u539F\u4EF7" }) }) }))] }), tableRows.map(function (item, index) { return ((0, jsx_runtime_1.jsx)(BookingItem_1.default, { rentDevice: booking === null || booking === void 0 ? void 0 : booking.rentDevice[index] }, index)); })] })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { marginTop: 14, marginBottom: 6 }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.title }, { children: "\u4E8C\u3001\u6682\u501F\u76EE\u7684\u533A\u522B" })) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { width: '100%', display: 'flex', flexDirection: 'row' } }, { children: (0, jsx_runtime_1.jsx)(CheckBox_1.default, { style: { marginRight: 4, lineHeight: 1.7 }, check: false, fontSize: '12pt', labelFontSize: '9pt', label: " \u7532\u65B9\u4E8E\u672A\u8D2D\u4E70\u524D\u987B\u6682\u501F\u8BBE\u5907\u4F53\u9A8C\uFF0C\u7ECF\u53CC\u65B9\u540C\u610F\uFF0C\u9002\u7528\u672C\u5207\u7ED3\u4E66\u4E09\u3001\u6761\u6B3E\u7B2C1\u30012\u6761\u53CA\u7B2C6\u6761\u81F3\u7B2C10\u6761\u3002" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { width: '100%', display: 'flex', flexDirection: 'row', marginTop: 5 } }, { children: (0, jsx_runtime_1.jsx)(CheckBox_1.default, { style: { marginRight: 4, lineHeight: 1.7 }, check: false, fontSize: '12pt', labelFontSize: '9pt', label: " \u7532\u65B9\u8BBE\u5907\u9001\u4FEE\uFF0C\u7ECF\u4E59\u65B9\u8BC4\u4F30\u540E\u987B\u6682\u501F\u8BBE\u5907\u66FF\u4EE3\u4F7F\u7528\uFF0C\u7ECF\u53CC\u65B9\u540C\u610F\uFF0C\u9002\u7528\u672C\u5207\u7ED3\u4E66\u4E09\u3001\u6761\u6B3E\u7B2C3\u81F3\u7B2C10\u6761\u3002" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { marginTop: 14, marginBottom: 6 }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.title }, { children: "\u4E09\u3001\u689D\u6B3E" })) })), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsxs)(renderer_1.Text, __assign({ style: styles.terms, fixed: true }, { children: ["1.\u7532\u65B9 ", (_e = booking === null || booking === void 0 ? void 0 : booking.member) === null || _e === void 0 ? void 0 : _e.name, " \u786E\u8BA4\u5DF2\u6536\u5230\u4E59\u65B9\u51FA\u501F\u7684\u4EE5\u4E0A\u5404\u9879\u8BBE\u5907\u8D27\u54C1 \uFF0C\u4E14\u5404\u9879\u8BBE\u5907\u8D27\u54C1\u5916\u89C2\u3001\u529F\u80FD\u7686\u5B8C\u6574\u826F\u597D\uFF0C\u5E76\u7ECF\u4E59\u65B9\u8BF4\u660E\u6307\u5BFC\u540E\u77E5\u6089"] })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 9 }) }, { children: "\u5404\u9879\u76F8\u5173\u64CD\u4F5C\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(table_1.Table, __assign({ style: { border: 'none', height: '35pt', marginLeft: -8, marginTop: -8 } }, { children: (0, jsx_runtime_1.jsxs)(table_1.TableRow, __assign({ style: { border: 'none', textAlign: 'left' } }, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '32%', border: 'none' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "2.\u7532\u65B9\u63D0\u4F9B\u8EAB\u5206\u8BC1\u539F\u4EF6\u6B63\u53CD\u9762\u590D\u5370\u4EF6\u53CA" })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '10%', border: 'none' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { marginLeft: -20, marginTop: 2 } }, { children: (0, jsx_runtime_1.jsx)(CheckBox_1.default, { style: { marginRight: 4, lineHeight: 1.7 }, check: false, fontSize: '9pt', labelFontSize: '9pt', label: " \u73B0\u91D1" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '10%', border: 'none' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { marginLeft: -38, marginTop: 2 } }, { children: (0, jsx_runtime_1.jsx)(CheckBox_1.default, { style: { marginRight: 4, lineHeight: 1.7 }, check: false, fontSize: '9pt', labelFontSize: '9pt', label: " \u5FAE\u4FE1" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '10%', border: 'none' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { marginLeft: -56, marginTop: 2 } }, { children: (0, jsx_runtime_1.jsx)(CheckBox_1.default, { style: { marginRight: 4, lineHeight: 1.7 }, check: false, fontSize: '9pt', labelFontSize: '9pt', label: " \u652F\u4ED8\u5B9D" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '10%', border: 'none' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { marginLeft: -68, marginTop: 2 } }, { children: (0, jsx_runtime_1.jsx)(CheckBox_1.default, { style: { marginRight: 4, lineHeight: 1.7 }, check: false, fontSize: '9pt', labelFontSize: '9pt', label: " \u94F6\u884C\u5361" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { border: 'none' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: -80 }) }, { children: [' ', "\u4EBA\u6C11\u5E01 __________ \u5143\u4F5C\u4E3A\u62BC\u91D1\u4F7F\u7528\u3002"] })) }))] })) })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginTop: -10 }) }, { children: "3.\u5179\u56E0\u7532\u65B9\u8BBE\u5907\u635F\u574F\u5411\u4E59\u65B9\u9001\u4FEE\uFF0C\u5728\u7EF4\u4FEE\u671F\u95F4\u6682\u501F\u8BBE\u5907\u66FF\u4EE3\u4F7F\u7528\uFF0C\u6682\u501F\u66FF\u4EE3\u4F7F\u7528\u4EE5\u4E59\u65B9\u5F53\u65F6\u73B0\u6709\u4E4B\u8BBE\u5907\u4E3A\u4E3B\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "4.\u8BBE\u5907\u7EF4\u4FEE\u5B8C\u6210\u540E\u4E59\u65B9\u987B\u8054\u7EDC\u7532\u65B9\u7EA6\u5B9A\u53D6\u4EF6\u65E5\u671F\uFF0C\u7532\u65B9\u5E94\u4F9D\u7EA6\u5230\u95E8\u5E02\u53D6\u56DE\u8BBE\u5907\uFF0C\u5B8C\u6210\u7EF4\u4FEE\u4ED8\u6B3E\u7A0B\u5E8F\uFF0C\u5E76\u5F52\u8FD8\u6682\u501F\u8BBE\u5907\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "5.\u9001\u4FEE\u540E\uFF0C\u5982\u7532\u65B9\u5728\u6536\u5230\u4E59\u65B9\u7684\u62A5\u4EF7\u901A\u77E5\u540E\uFF0C\u8BF7\u4E8E\u4E03\u4E2A\u5DE5\u4F5C\u5929\u5185\u56DE\u8986\u662F\u5426\u7EF4\u4FEE\u6216\u53E6\u884C\u4E0E\u4E59\u65B9\u7EA6\u5B9A\u56DE\u8986\u65F6\u95F4\u3002 \u4E59\u65B9\u81F3\u591A\u4FDD\u7559\u8BE5\u7EF4\u4FEE\u4EF6" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 9 }) }, { children: "30\u4E2A\u5DE5\u4F5C\u65E5\uFF08\u81EA\u4E59\u65B9\u7EF4\u4FEE\u62A5\u4EF7\u65E5\u8D77\u7B97\uFF09\u4E3A\u4FDD\u7559\u671F\uFF0C\u4F46\u82E5\u9047\u7532\u65B9\u7EF4\u4FEE\u96F6\u4EF6\u5F85\u6599\u4E0D\u5728\u6B64\u9650\u3002\u4F46\u4E8E\u4FDD\u7559\u671F\u8FC7\u540E\uFF0C \u7532\u65B9\u4ECD\u65E0\u56DE\u8986\u662F\u5426\u7EF4\u4FEE\u6216" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 9 }) }, { children: "\u53D6\u4EF6\uFF0C\u5219\u7532\u65B9\u540C\u610F\u4EE5\u65B9\u6709\u6743\u5C06\u6240\u6536\u53D6\u4E4B\u6682\u501F\u8BBE\u5907\u62BC\u91D1\u8F6C\u4E3A\u7532\u65B9\u8D2D\u4E70\u6682\u501F\u8BBE\u5907\uFF08\u65E0\u4FDD\u56FA\u671F\uFF09\u4E4B\u8D27\u6B3E\u5165\u5E10 \uFF0C\u4E0D\u518D\u5F52\u8FD8\u7532\u65B9\uFF0C\u62BC\u91D1\u4E0D\u8DB3\u8D27\u6B3E" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 9 }) }, { children: "\u65F6\uFF0C\u7532\u65B9\u4ECD\u987B\u652F\u4ED8\u4E0D\u8DB3\u4E4B\u8D27\u6B3E\uFF0C\u53E6\u7532\u65B9\u9001\u4FEE\u4E4B\u673A\u5668\u540C\u610F\u7531\u4E59\u65B9\u5904\u7F6E\uFF0C\u7532\u65B9\u7EDD\u65E0\u5F02\u8BAE\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "6.\u53CC\u65B9\u540C\u610F\u5728\u5B9E\u9645\u5F52\u8FD8\u65E5\u671F\u7531\u7532\u65B9\u5F52\u8FD8\u6682\u501F\u4E4B\u5404\u9879\u8BBE\u5907\uFF0C\u5982\u7532\u65B9\u672A\u4E8E\u5F52\u8FD8\u65E5\u671F\u524D\u5F52\u8FD8\u6240\u6709\u8BBE\u5907\u8D27\u54C1\uFF0C\u540C\u610F\u89C6\u540C\u8D2D\u4E70\u6682\u501F\u8BBE\u5907\u8D27\u54C1\u8005" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 9 }) }, { children: "\uFF0C\u4E59\u65B9\u6709\u6743\u5C06\u6240\u6536\u53D6\u4E4B\u6682\u501F\u8BBE\u5907\u8D27\u54C1\u4E4B\u62BC\u91D1\u8F6C\u4E3A\u7532\u65B9\u8D2D\u4E70\u4E4B\u8D27\u6B3E\uFF0C\u4E0D\u518D\u5F52\u8FD8\u7532\u65B9\uFF0C\u62BC\u91D1\u4E0D\u8DB3\u8D27\u6B3E\u65F6\uFF0C\u7532\u65B9\u4ECD\u987B\u652F\u4ED8\u4E0D\u8DB3\u4E4B\u8D27\u6B3E\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "7.\u7532\u65B9\u987B\u5584\u5C3D\u5BF9\u6682\u501F\u8BBE\u5907\u4E4B\u4FDD\u7BA1\u8D23\u4EFB\uFF0C\u82E5\u7532\u65B9\u9057\u5931\u8BBE\u5907\u8D27\u54C1\uFF0C\u5219\u987B\u6309\u7167\u6682\u501F\u8BBE\u5907\u8D27\u54C1\u724C\u4EF7 ____________________ \u5168\u989D\u8D54\u507F\uFF0C\u7532\u65B9\u540C\u610F" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 9 }) }, { children: "\u62BC\u91D1\u4E0D\u8DB3\u8D27\u6B3E\u65F6\uFF0C\u7532\u65B9\u4ECD\u987B\u652F\u4ED8\u4E0D\u8DB3\u4E4B\u8D27\u6B3E\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "8.\u4E8E\u6682\u501F\u671F\u95F4\uFF0C\u5982\u6682\u501F\u4E4B\u8BBE\u5907\u8D27\u54C1\u4EA7\u751F\u975E\u4EBA\u4E3A\u635F\u574F\uFF0C\u8BF7\u7532\u65B9\u7ACB\u5373\u4E0E\u4E59\u65B9\u8054\u7EDC\u66F4\u6362\u6682\u501F\u8BBE\u5907\u8D27\u54C1\u3002\u4F46\u5982\u56E0\u4EBA\u4E3A\u635F\u574F\uFF0C \u7532\u65B9\u5C06\u8D1F\u5B8C\u5168\u4E4B\u4FEE" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 9 }) }, { children: "\u62A4\u8D54\u507F\u8D23\u4EFB\u5E76\u540C\u610F\u4FEE\u62A4\u4E4B\u8D39\u7528\u7531\u6240\u652F\u4ED8\u4E4B\u62BC\u91D1\u5E95\u6263\uFF0C\u4E0D\u8DB3\u5219\u53E6\u652F\u4ED8\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "9.\u7532\u65B9\u987B\u5584\u5C3D\u5BF9\u6682\u501F\u8BBE\u5907\u65E5\u5E38\u4FDD\u517B\u53CA\u7EF4\u62A4\u4E4B\u8D23\u4EFB\uFF0C\u529E\u7406\u5F52\u8FD8\u65F6\u7531\u4E59\u65B9\u786E\u8BA4\u8BBE\u5907\u5916\u89C2\u3001\u529F\u80FD \u7686\u5B8C\u6574\u826F\u597D\u65E0\u8BEF\u540E\uFF0C\u4E59\u65B9\u987B\u5C06\u62BC\u91D1\u8EAB\u4EFD\u6B63\u53CD\u9762" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 9 }) }, { children: "\u590D\u5370\u4EF6\u53CD\u8FD8\u7532\u65B9\uFF0C\u5E76\u7ECF\u53CC\u65B9\u7B7E\u540D\u786E\u8BA4\u3002\u5982\u672A\u7ECF\u53CC\u65B9\u786E\u8BA4\u7B7E\u540D\uFF0C\u89C6\u540C\u672A\u5B8C\u6210\u5F52\u8FD8\u7A0B\u5E8F\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "10.\u4E59\u65B9\u63A8\u5E7F\u6D3B\u52A8\u4E4B\u9650\u5B9A\u8BBE\u5907\uFF0C\u53EF\u514D\u62BC\u91D1\u6682\u501F\u3002" })) }), (0, jsx_runtime_1.jsxs)(table_1.Table, __assign({ style: { marginTop: 5, marginBottom: 6 } }, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6682\u501F\u8005(\u7532\u65B9):" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u501F\u51FA\u8005(\u4E59\u65B9):" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '12%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u95E8\u5E02:" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '14%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })) }))] }), (0, jsx_runtime_1.jsx)(table_1.TableRow, { children: (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '100%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6682\u501F\u8005(\u7532\u65B9): ____________________ \u5DF2\u5F52\u8FD8\u4E0A\u8FF0\u5404\u9879\u8D27\u54C1\uFF0C\u5E76\u5DF2\u6536\u5230\u4E0A\u8FF0\u5168\u6570\u62BC\u91D1\u91D1\u989D\u53CA\u8EAB\u4EFD\u8BC1\u6B63\u53CD\u9762\u590D\u5370\u4EF6" }) })) })) }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6536\u8D27\u8005:" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u95E8\u5E02:" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '12%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u65E5\u671F:" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '14%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })) }))] })] })), !hiddenFooter && ((0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { margin: '8px 0' } }, { children: (0, jsx_runtime_1.jsx)(ISOFooter_1.default, { date: docInfo.date, version: docInfo.version, fileCode: docInfo.fileCode, lang: lang }) })))] })));
};
exports.default = BookingPageCN;
