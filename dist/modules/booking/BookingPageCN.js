"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var table_1 = require("../../components/table");
var consts_1 = require("../../consts");
var ISOFooter_1 = __importDefault(require("../common/ISOFooter"));
var BookingItem_1 = __importDefault(require("./BookingItem"));
var CheckBox_1 = __importDefault(require("../common/CheckBox"));
renderer_1.Font.register({
    family: consts_1.TRADITIONAL_CHINESE,
    src: 'SourceHanSansHWTC-VF.ttf',
});
renderer_1.Font.registerHyphenationCallback(function (word) { return word.split(''); });
var tableRows = [0, 1, 2];
var styles = renderer_1.StyleSheet.create({
    page: {
        fontFamily: consts_1.TRADITIONAL_CHINESE,
        backgroundColor: 'white',
        fontSize: 9,
        padding: '8mm 10mm',
    },
    row: {
        display: 'flex',
        flexDirection: 'row',
    },
    title: {
        fontSize: 11,
        fontWeight: 'bold',
    },
    terms: {
        fontSize: 9,
        lineHeight: '1.5',
    },
});
// TODO ISO文件審核的版本號,等待審核完畢要回來更新
var docInfo = {
    date: '2023-02-28',
    version: '1',
    fileCode: 'C-SC-N-4-7-036',
};
var BookingPageCN = function (props) {
    var _a, _b, _c, _d, _e;
    var companyTitle = props.companyTitle, booking = props.booking, _f = props.hiddenFooter, hiddenFooter = _f === void 0 ? false : _f, lang = props.lang;
    return ((0, jsx_runtime_1.jsxs)(renderer_1.Page, __assign({ size: "A4", style: styles.page }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { style: { textAlign: 'right', fontSize: '6pt' }, fixed: true, render: function (_a) {
                    var pageNumber = _a.pageNumber;
                    return pageNumber === 1 && "\u7B2C\u4E00\u8054(\u95E8\u5E02\u4EBA\u5458\u6536\u6267)";
                } }), (0, jsx_runtime_1.jsx)(renderer_1.Text, { style: { textAlign: 'right', fontSize: '6pt' }, fixed: true, render: function (_a) {
                    var pageNumber = _a.pageNumber;
                    return pageNumber === 2 && "\u7B2C\u4E8C\u8054(\u7528\u6237\u7559\u5B58\u6536\u6267)";
                } }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ fixed: true }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { textAlign: 'center', marginBottom: '2mm' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { fontSize: '20pt', lineHeight: '1.5', fontWeight: 'bold' } }, { children: "\u6682\u501F\u5207\u7ED3\u4E66" })) })) })), (0, jsx_runtime_1.jsxs)(table_1.Table, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u7528\u6237\u59D3\u540D(\u7532\u65B9):" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '31.8%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_a = booking === null || booking === void 0 ? void 0 : booking.member) === null || _a === void 0 ? void 0 : _a.name }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u4F1A \u5458 \u7F16 \u53F7:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '35%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_b = booking === null || booking === void 0 ? void 0 : booking.member) === null || _b === void 0 ? void 0 : _b.memberCode }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8054 \u7EDC \u5730 \u5740:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '48.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_c = booking === null || booking === void 0 ? void 0 : booking.member) === null || _c === void 0 ? void 0 : _c.address }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u8054 \u7EDC \u7535 \u8BDD:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '18.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: ((_d = booking === null || booking === void 0 ? void 0 : booking.member) === null || _d === void 0 ? void 0 : _d.mobile) || ((_e = booking === null || booking === void 0 ? void 0 : booking.member) === null || _e === void 0 ? void 0 : _e.phone) }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u501F \u51FA \u65E5 \u671F:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '31.8%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: booking === null || booking === void 0 ? void 0 : booking.rentStartDate }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u5F52 \u8FD8 \u65E5 \u671F:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '35%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: booking === null || booking === void 0 ? void 0 : booking.rentEndDate }) }))] })] }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { marginTop: 16, marginBottom: 4 }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.title }, { children: "\u4E00\u3001\u6682\u501F\u4EA7\u54C1\u54C1\u9879" })) })), (0, jsx_runtime_1.jsxs)(table_1.Table, __assign({ wrap: false }, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u4EA7\u54C1" }) }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '40%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u578B\u53F7" }) }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u673A\u5668\u5E8F\u53F7" }) }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '10%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6570\u91CF" }) }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u539F\u4EF7" }) }) }))] }), tableRows.map(function (item, index) { return ((0, jsx_runtime_1.jsx)(BookingItem_1.default, { rentDevice: booking === null || booking === void 0 ? void 0 : booking.rentDevice[index] }, index)); })] })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { marginTop: 14, marginBottom: 6 }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.title }, { children: "\u4E8C\u3001\u6682\u501F\u76EE\u7684\u533A\u5206(\u52FE\u9009)" })) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { width: '100%', display: 'flex', flexDirection: 'row' } }, { children: (0, jsx_runtime_1.jsx)(CheckBox_1.default, { style: { marginRight: 4, lineHeight: 1.7 }, check: false, fontSize: '12pt', labelFontSize: '9pt', label: " \u7532\u65B9\u4E8E\u672A\u8D2D\u4E70\u524D\u987B\u6682\u501F\u8BBE\u5907\u4F53\u9A8C\uFF0C\u7ECF\u53CC\u65B9\u540C\u610F\uFF0C\u9002\u7528\u672C\u5207\u7ED3\u4E66\u4E09\u3001\u6761\u6B3E\u7B2C1\u30012\u6761\u53CA\u7B2C6\u6761\u81F3\u7B2C10\u6761\u3002" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { width: '100%', display: 'flex', flexDirection: 'row', marginTop: 5 } }, { children: (0, jsx_runtime_1.jsx)(CheckBox_1.default, { style: { marginRight: 4, lineHeight: 1.7 }, check: false, fontSize: '12pt', labelFontSize: '9pt', label: " \u7532\u65B9\u8BBE\u5907\u9001\u4FEE\uFF0C\u7ECF\u4E59\u65B9\u8BC4\u4F30\u540E\u987B\u6682\u501F\u8BBE\u5907\u66FF\u4EE3\u4F7F\u7528\uFF0C\u7ECF\u53CC\u65B9\u540C\u610F\uFF0C\u9002\u7528\u672C\u5207\u7ED3\u4E66\u4E09\u3001\u6761\u6B3E\u7B2C2\u6761\u81F3\u7B2C10\u6761\u3002" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { marginTop: 14, marginBottom: 6 }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.title }, { children: "\u4E09\u3001\u689D\u6B3E" })) })), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "1.\u7532\u65B9\u786E\u8BA4\u5DF2\u6536\u5230\u4E59\u65B9\u51FA\u501F\u4EE5\u4E0A\u5404\u9879\u8BBE\u5907\u8D27\u54C1\uFF0C\u5E76\u786E\u8BA4\u5176\u5916\u89C2\u53CA\u529F\u80FD\u5747\u5C5E\u5B8C\u6574\u826F\u597D\uFF0C\u4E14\u7ECF\u4E59\u65B9\u4EBA\u5458\u8BF4\u660E\u6307\u5BFC\u540E\uFF0C\u5DF2\u5145\u5206\u4E86\u89E3\u76F8\u5173\u64CD\u4F5C\u65B9\u5F0F\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "2.\u7532\u65B9\u5DF2\u63D0\u4F9B\u8EAB\u4EFD\u8BC1\u6B63\u53CD\u9762\u590D\u5370\u4EF6\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "3.\u56E0\u7532\u65B9\u539F\u8BBE\u5907\u635F\u574F\u9001\u4FEE\uFF0C\u4E59\u65B9\u4E8E\u7EF4\u4FEE\u671F\u95F4\u51FA\u501F\u6682\u501F\u8BBE\u5907\u4F9B\u66FF\u4EE3\u4F7F\u7528\uFF0C\u5176\u578B\u53F7\u3001\u529F\u80FD\u53CA\u89C4\u683C\u4EE5\u4E59\u65B9\u5F53\u65F6\u73B0\u6709\u8BBE\u5907\u4E3A\u51C6\uFF0C\u7532\u65B9\u5E94\u4E88\u914D\u5408\u4F7F\u7528\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "4.\u539F\u8BBE\u5907\u7EF4\u4FEE\u5B8C\u6210\u540E\uFF0C\u4E59\u65B9\u5E94\u901A\u77E5\u7532\u65B9\u7EA6\u5B9A\u53D6\u4EF6\u65E5\u671F\uFF0C\u7532\u65B9\u5E94\u4F9D\u7EA6\u81F3\u4E59\u65B9\u501F\u51FA\u95E8\u5E02\u53D6\u56DE\u8BBE\u5907\u5E76\u5B8C\u6210\u7EF4\u4FEE\u4ED8\u6B3E\u7A0B\u5E8F\uFF0C\u540C\u65F6\u5F52\u8FD8\u6682\u501F\u8BBE\u5907\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "5.\u7532\u65B9\u4E8E\u6536\u5230\u4E59\u65B9\u62A5\u4EF7\u901A\u77E5\u540E\uFF0C\u5E94\u4E8E\u4E03\u4E2A\u5DE5\u4F5C\u5929\u5185\u56DE\u8986\u662F\u5426\u540C\u610F\u7EF4\u4FEE\uFF0C\u6216\u53E6\u884C\u4E0E\u4E59\u65B9\u7EA6\u5B9A\u56DE\u8986\u671F\u9650\u3002\u4E59\u65B9\u5F97\u81EA\u62A5\u4EF7\u65E5\u8D77\u4FDD\u7559\u8BE5\u9001\u4FEE\u539F\u8BBE\u5907\u6700\u957F\u4E09\u5341" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 9 }) }, { children: "\u4E2A\u5DE5\u4F5C\u5929\uFF0C\u5982\u56E0\u7532\u65B9\u7EF4\u4FEE\u9879\u76EE\u6D89\u53CA\u96F6\u4EF6\u5F85\u6599\uFF0C\u5219\u53CC\u65B9\u5E94\u53E6\u884C\u7EA6\u5B9A\u4FDD\u7559\u671F\u3002\u4E8E\u4FDD\u7559\u671F\u8FC7\u540E\uFF0C\u7532\u65B9\u4ECD\u65E0\u56DE\u8986\u662F\u5426\u7EF4\u4FEE\u6216\u53D6\u4EF6\uFF0C\u5219\u7532\u65B9\u540C\u610F\u4E59\u65B9\u5F97\u89C6\u540C" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 9 }) }, { children: "\u7532\u65B9\u4E0D\u8FDB\u884C\u7EF4\u4FEE\uFF0C\u539F\u8BBE\u5907\u5C06\u53E6\u884C\u6682\u65F6\u4FDD\u7BA1\u5E76\u901A\u77E5\u7532\u65B9\u518D\u786E\u8BA4\u5904\u7F6E\u65B9\u5F0F\uFF0C\u5FC5\u8981\u65F6\u5F97\u6536\u53D6\u4FDD\u7BA1\u8D39\u7528\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "6.\u53CC\u65B9\u540C\u610F\u5728\u4E0A\u8FF0\u5F52\u8FD8\u65E5\u671F\u7531\u7532\u65B9\u5F52\u8FD8\u6682\u501F\u4E4B\u5404\u9879\u8BBE\u5907\uFF0C\u5982\u7532\u65B9\u672A\u4E8E\u5F52\u8FD8\u65E5\u671F\u524D\u5F52\u8FD8\u6240\u6709\u8BBE\u5907\u8D27\u54C1\uFF0C\u89C6\u4E3A\u9057\u5931\uFF0C\u5E94\u4F9D\u672C\u6682\u501F\u5408\u7EA6\u4E66\u7B2C\u4E00\u6761\u6240\u5217\u4E4B\u552E" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 9 }) }, { children: "\u4EF7\u8D54\u507F\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "7.\u7532\u65B9\u5E94\u5584\u5C3D\u5BF9\u6682\u501F\u8BBE\u5907\u4E4B\u4FDD\u7BA1\u4E49\u52A1\u3002\u5982\u6709\u9057\u5931\u60C5\u5F62\uFF0C\u5E94\u4F9D\u672C\u6682\u501F\u5408\u7EA6\u4E66\u7B2C\u4E00\u6761\u6240\u5217\u4E4B\u552E\u4EF7\u8D54\u507F\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "8.\u6682\u501F\u671F\u95F4\uFF0C\u5982\u8BBE\u5907\u53D1\u751F\u975E\u4EBA\u4E3A\u635F\u574F\uFF0C\u7532\u65B9\u5E94\u7ACB\u5373\u901A\u77E5\u4E59\u65B9\u66F4\u6362\u8BBE\u5907\u3002\u4E59\u65B9\u7ECF\u68C0\u6D4B\u5E76\u5408\u7406\u5224\u5B9A\u4E3A\u4EBA\u4E3A\u635F\u574F\uFF0C\u5E76\u63D0\u4F9B\u76F8\u5173\u8BF4\u660E\u540E\uFF0C\u7532\u65B9\u5E94\u8D1F\u5168\u90E8\u4FEE" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 9 }) }, { children: "\u590D\u8D23\u4EFB\uFF0C\u4F46\u4EE5\u672C\u6682\u501F\u5408\u7EA6\u4E66\u7B2C\u4E00\u6761\u6240\u5217\u4E4B\u552E\u4EF7\u4E3A\u4E0A\u9650\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "9.\u7532\u65B9\u5E94\u5584\u5C3D\u5BF9\u65E5\u5E38\u4FDD\u517B\u53CA\u7EF4\u62A4\u4E4B\u8D23\u4EFB\uFF0C\u5F52\u8FD8\u6682\u501F\u8BBE\u5907\u65F6\uFF0C\u987B\u7ECF\u4E59\u65B9\u786E\u8BA4\u6682\u501F\u8BBE\u5907\u5916\u89C2\u4E0E\u529F\u80FD\u65E0\u8BEF\uFF0C\u5E76\u5E94\u7531\u53CC\u65B9\u7B7E\u540D\u786E\u8BA4\u3002\u5982\u672A\u7ECF\u53CC\u65B9\u7B7E\u7F72\u786E\u8BA4\uFF0C" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 9 }) }, { children: "\u89C6\u4E3A\u5C1A\u672A\u5B8C\u6210\u5F52\u8FD8\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "10.\u672C\u6682\u501F\u5408\u7EA6\u4E66\u56E0\u5C65\u884C\u6240\u751F\u4E4B\u4E00\u5207\u4E89\u8BAE\uFF0C\u53CC\u65B9\u540C\u610F\u4EE5\u4E2D\u534E\u6C11\u56FD\u6CD5\u5F8B\u4E3A\u51C6\u636E\u6CD5\uFF0C\u5E76\u4EE5\u4E59\u65B9\u501F\u51FA\u95E8\u5E02\u6240\u5728\u5730\u5730\u65B9\u6CD5\u9662\u4E3A\u7B2C\u4E00\u5BA1\u7BA1\u8F96\u6CD5\u9662\u3002\u60DF\u7532\u65B9\u4E3A\u6D88\u8D39" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 12 }) }, { children: "\u8005\u8005\uFF0C\u4ECD\u5F97\u4F9D\u6D88\u8D39\u8005\u4FDD\u62A4\u6CD5\u7B2C47\u6761\u89C4\u5B9A\u8FDB\u884C\u3002" })) }), (0, jsx_runtime_1.jsxs)(table_1.Table, __assign({ style: { marginTop: 5, marginBottom: 6 } }, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6682\u501F\u8005(\u7532\u65B9):" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u501F\u51FA\u8005(\u4E59\u65B9):" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '12%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u95E8\u5E02:" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '14%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })) }))] }), (0, jsx_runtime_1.jsx)(table_1.TableRow, { children: (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '100%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6682\u501F\u8005(\u7532\u65B9): ____________________ \u5DF2\u5F52\u8FD8\u4E0A\u8FF0\u5404\u9879\u8D27\u54C1\uFF0C\u5E76\u5DF2\u6536\u5230\u4E0A\u8FF0\u5168\u6570\u62BC\u91D1\u91D1\u989D\u53CA\u8EAB\u4EFD\u8BC1\u6B63\u53CD\u9762\u590D\u5370\u4EF6" }) })) })) }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6536\u8D27\u8005:" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u95E8\u5E02:" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '12%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u65E5\u671F:" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '14%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })) }))] })] })), !hiddenFooter && ((0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { margin: '8px 0' } }, { children: (0, jsx_runtime_1.jsx)(ISOFooter_1.default, { date: docInfo.date, version: docInfo.version, fileCode: docInfo.fileCode, lang: lang }) })))] })));
};
exports.default = BookingPageCN;
