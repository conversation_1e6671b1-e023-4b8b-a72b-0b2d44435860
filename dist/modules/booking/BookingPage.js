"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var table_1 = require("../../components/table");
var consts_1 = require("../../consts");
var ISOFooter_1 = __importDefault(require("../common/ISOFooter"));
var BookingItem_1 = __importDefault(require("./BookingItem"));
var CheckBox_1 = __importDefault(require("../common/CheckBox"));
var path_1 = __importDefault(require("path"));
renderer_1.Font.register({
    family: consts_1.TRADITIONAL_CHINESE,
    fonts: [
        {
            src: path_1.default.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'bold',
        },
        {
            src: path_1.default.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'medium',
        },
        {
            src: path_1.default.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'regular',
        },
    ],
});
renderer_1.Font.registerHyphenationCallback(function (word) { return word.split(''); });
var tableRows = [0, 1, 2];
var styles = renderer_1.StyleSheet.create({
    page: {
        fontFamily: consts_1.TRADITIONAL_CHINESE,
        backgroundColor: 'white',
        fontSize: 9,
        padding: '8mm 10mm',
    },
    row: {
        display: 'flex',
        flexDirection: 'row',
    },
    title: {
        fontSize: 11,
        fontWeight: 'bold',
    },
    terms: {
        fontSize: 9,
        lineHeight: '1.5',
    },
});
var BookingPage = function (props) {
    var _a, _b, _c, _d, _e, _f, _g, _h;
    var companyTitle = props.companyTitle, booking = props.booking, _j = props.hiddenFooter, hiddenFooter = _j === void 0 ? false : _j, lang = props.lang;
    return ((0, jsx_runtime_1.jsxs)(renderer_1.Page, __assign({ size: "A4", style: styles.page }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, { style: { textAlign: 'right', fontSize: '6pt' }, fixed: true, render: function (_a) {
                    var pageNumber = _a.pageNumber;
                    return pageNumber === 1 && "\u7B2C\u4E00\u806F(\u9580\u5E02\u4EBA\u54E1\u6536\u57F7)";
                } }), (0, jsx_runtime_1.jsx)(renderer_1.Text, { style: { textAlign: 'right', fontSize: '6pt' }, fixed: true, render: function (_a) {
                    var pageNumber = _a.pageNumber;
                    return pageNumber === 2 && "\u7B2C\u4E8C\u806F(\u7528\u6236\u7559\u5B58\u6536\u57F7)";
                } }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ fixed: true }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, __assign({ style: { textAlign: 'center', marginBottom: '2mm' } }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { fontSize: '22pt', lineHeight: '1.5', fontWeight: 'bold' } }, { children: companyTitle })), (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { fontSize: '16pt', lineHeight: '1.5' } }, { children: "\u66AB\u501F\u5207\u7D50\u66F8" }))] })) })), (0, jsx_runtime_1.jsxs)(table_1.Table, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u7528\u6236\u59D3\u540D(\u7532\u65B9):" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '31.8%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_a = booking === null || booking === void 0 ? void 0 : booking.member) === null || _a === void 0 ? void 0 : _a.name }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6703 \u54E1 \u7DE8 \u865F:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '35%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_b = booking === null || booking === void 0 ? void 0 : booking.member) === null || _b === void 0 ? void 0 : _b.memberCode }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u806F \u7D61 \u5730 \u5740:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '48.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_c = booking === null || booking === void 0 ? void 0 : booking.member) === null || _c === void 0 ? void 0 : _c.address }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u806F \u7D61 \u96FB \u8A71:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '18.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: ((_d = booking === null || booking === void 0 ? void 0 : booking.member) === null || _d === void 0 ? void 0 : _d.mobile) || ((_e = booking === null || booking === void 0 ? void 0 : booking.member) === null || _e === void 0 ? void 0 : _e.phone) }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u501F \u51FA \u65E5 \u671F:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '31.8%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: booking === null || booking === void 0 ? void 0 : booking.rentStartDate }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6B78 \u9084 \u65E5 \u671F:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '35%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: booking === null || booking === void 0 ? void 0 : booking.rentEndDate }) }))] })] }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { marginTop: 17, marginBottom: 4 }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.title }, { children: "\u4E00\u3001\u66AB\u501F\u7522\u54C1\u54C1\u9805" })) })), (0, jsx_runtime_1.jsxs)(table_1.Table, __assign({ wrap: false }, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u7522\u54C1" }) }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '40%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u578B\u865F" }) }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6A5F\u5668\u5E8F\u865F" }) }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '10%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6578\u91CF" }) }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u724C\u50F9" }) }) }))] }), tableRows.map(function (item, index) { return ((0, jsx_runtime_1.jsx)(BookingItem_1.default, { rentDevice: booking === null || booking === void 0 ? void 0 : booking.rentDevice[index] }, index)); })] })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { marginTop: 17, marginBottom: 6 }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.title }, { children: "\u4E8C\u3001\u66AB\u501F\u76EE\u7684\u5340\u5206(\u52FE\u9078)" })) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { width: '100%', display: 'flex', flexDirection: 'row' } }, { children: (0, jsx_runtime_1.jsx)(CheckBox_1.default, { style: { marginRight: 4, lineHeight: 1.7 }, check: false, fontSize: '12pt', labelFontSize: '9pt', label: " \u7532\u65B9\u65BC\u672A\u8CFC\u8CB7\u524D\u9808\u66AB\u501F\u8A2D\u5099\u9AD4\u9A57\uFF0C\u7D93\u96D9\u65B9\u540C\u610F\uFF0C\u9069\u7528\u672C\u5207\u7D50\u66F8\u4E09\u3001\u689D\u6B3E\u7B2C1\u30012\u689D\u53CA\u7B2C6\u689D\u81F3\u7B2C10\u689D\u3002" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { width: '100%', display: 'flex', flexDirection: 'row', marginTop: 5 } }, { children: (0, jsx_runtime_1.jsx)(CheckBox_1.default, { style: { marginRight: 4, lineHeight: 1.7 }, check: false, fontSize: '12pt', labelFontSize: '9pt', label: " \u7532\u65B9\u8A2D\u5099\u9001\u4FEE\uFF0C\u7D93\u4E59\u65B9\u8A55\u4F30\u5F8C\u9808\u66AB\u501F\u8A2D\u5099\u66FF\u4EE3\u4F7F\u7528\uFF0C\u7D93\u96D9\u65B9\u540C\u610F\uFF0C\u9069\u7528\u672C\u5207\u7D50\u66F8\u4E09\u3001\u689D\u6B3E\u7B2C2\u689D\u81F3\u7B2C10\u689D\u3002" }) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { marginTop: 18, marginBottom: 6 }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.title }, { children: "\u4E09\u3001\u689D\u6B3E" })) })), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "1.\u7532\u65B9\u78BA\u8A8D\u5DF2\u6536\u5230\u4E59\u65B9\u51FA\u501F\u4EE5\u4E0A\u5404\u9805\u8A2D\u5099\u8CA8\u54C1\uFF0C\u4E26\u78BA\u8A8D\u5176\u5916\u89C0\u53CA\u529F\u80FD\u5747\u5C6C\u5B8C\u6574\u826F\u597D\uFF0C\u4E14\u7D93\u4E59\u65B9\u4EBA\u54E1\u8AAA\u660E\u6307\u5C0E\u5F8C\uFF0C\u5DF2\u5145\u5206\u4E86\u89E3\u76F8\u95DC\u64CD\u4F5C\u65B9\u5F0F\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "2.\u7532\u65B9\u5DF2\u63D0\u4F9B\u8EAB\u4EFD\u8B49\u6B63\u53CD\u9762\u5F71\u672C\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "3.\u56E0\u7532\u65B9\u539F\u8A2D\u5099\u640D\u58DE\u9001\u4FEE\uFF0C\u4E59\u65B9\u65BC\u7DAD\u4FEE\u671F\u9593\u51FA\u501F\u66AB\u501F\u8A2D\u5099\u4F9B\u66FF\u4EE3\u4F7F\u7528\uFF0C\u5176\u578B\u865F\u3001\u529F\u80FD\u53CA\u898F\u683C\u4EE5\u4E59\u65B9\u7576\u6642\u73FE\u6709\u8A2D\u5099\u70BA\u6E96\uFF0C\u7532\u65B9\u61C9\u4E88\u914D\u5408\u4F7F\u7528\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "4.\u539F\u8A2D\u5099\u7DAD\u4FEE\u5B8C\u6210\u5F8C\uFF0C\u4E59\u65B9\u61C9\u901A\u77E5\u7532\u65B9\u7D04\u5B9A\u53D6\u4EF6\u65E5\u671F\uFF0C\u7532\u65B9\u61C9\u4F9D\u7D04\u81F3\u4E59\u65B9\u501F\u51FA\u9580\u5E02\u53D6\u56DE\u8A2D\u5099\u4E26\u5B8C\u6210\u7DAD\u4FEE\u4ED8\u6B3E\u7A0B\u5E8F\uFF0C\u540C\u6642\u6B78\u9084\u66AB\u501F\u8A2D\u5099\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "5.\u7532\u65B9\u65BC\u6536\u5230\u4E59\u65B9\u5831\u50F9\u901A\u77E5\u5F8C\uFF0C\u61C9\u65BC\u4E03\u500B\u5DE5\u4F5C\u5929\u5167\u56DE\u8986\u662F\u5426\u540C\u610F\u7DAD\u4FEE\uFF0C\u6216\u53E6\u884C\u8207\u4E59\u65B9\u7D04\u5B9A\u56DE\u8986\u671F\u9650\u3002\u4E59\u65B9\u5F97\u81EA\u5831\u50F9\u65E5\u8D77\u4FDD\u7559\u8A72\u9001\u4FEE\u539F\u8A2D\u5099\u6700\u9577\u4E09\u5341" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 9 }) }, { children: "\u500B\u5DE5\u4F5C\u5929\uFF0C\u5982\u56E0\u7532\u65B9\u7DAD\u4FEE\u9805\u76EE\u6D89\u53CA\u96F6\u4EF6\u5F85\u6599\uFF0C\u5247\u96D9\u65B9\u61C9\u53E6\u884C\u7D04\u5B9A\u4FDD\u7559\u671F\u3002\u65BC\u4FDD\u7559\u671F\u904E\u5F8C\uFF0C\u7532\u65B9\u4ECD\u7121\u56DE\u8986\u662F\u5426\u7DAD\u4FEE\u6216\u53D6\u4EF6\uFF0C\u5247\u7532\u65B9\u540C\u610F\u4E59\u65B9\u5F97\u8996\u540C" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 9 }) }, { children: "\u7532\u65B9\u4E0D\u9032\u884C\u7DAD\u4FEE\uFF0C\u539F\u8A2D\u5099\u5C07\u53E6\u884C\u66AB\u6642\u4FDD\u7BA1\u4E26\u901A\u77E5\u7532\u65B9\u518D\u78BA\u8A8D\u8655\u7F6E\u65B9\u5F0F\uFF0C\u5FC5\u8981\u6642\u5F97\u6536\u53D6\u4FDD\u7BA1\u8CBB\u7528\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "6.\u96D9\u65B9\u540C\u610F\u5728\u4E0A\u8FF0\u6B78\u9084\u65E5\u671F\u7531\u7532\u65B9\u6B78\u9084\u66AB\u501F\u4E4B\u5404\u9805\u8A2D\u5099\uFF0C\u5982\u7532\u65B9\u672A\u65BC\u6B78\u9084\u65E5\u671F\u524D\u6B78\u9084\u6240\u6709\u8A2D\u5099\u8CA8\u54C1\uFF0C\u8996\u70BA\u907A\u5931\uFF0C\u61C9\u4F9D\u672C\u66AB\u501F\u5408\u7D04\u66F8\u7B2C\u4E00\u689D\u6240\u5217\u4E4B\u552E" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 9 }) }, { children: "\u50F9\u8CE0\u511F\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "7.\u7532\u65B9\u61C9\u5584\u76E1\u5C0D\u66AB\u501F\u8A2D\u5099\u4E4B\u4FDD\u7BA1\u7FA9\u52D9\u3002\u5982\u6709\u907A\u5931\u60C5\u5F62\uFF0C\u61C9\u4F9D\u672C\u66AB\u501F\u5408\u7D04\u66F8\u7B2C\u4E00\u689D\u6240\u5217\u4E4B\u552E\u50F9\u8CE0\u511F\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "8.\u66AB\u501F\u671F\u9593\uFF0C\u5982\u8A2D\u5099\u767C\u751F\u975E\u4EBA\u70BA\u640D\u58DE\uFF0C\u7532\u65B9\u61C9\u7ACB\u5373\u901A\u77E5\u4E59\u65B9\u66F4\u63DB\u8A2D\u5099\u3002\u4E59\u65B9\u7D93\u6AA2\u6E2C\u4E26\u5408\u7406\u5224\u5B9A\u70BA\u4EBA\u70BA\u640D\u58DE\uFF0C\u4E26\u63D0\u4F9B\u76F8\u95DC\u8AAA\u660E\u5F8C\uFF0C\u7532\u65B9\u61C9\u8CA0\u5168\u90E8\u4FEE" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 9 }) }, { children: "\u5FA9\u8CAC\u4EFB\uFF0C\u4F46\u4EE5\u672C\u66AB\u501F\u5408\u7D04\u66F8\u7B2C\u4E00\u689D\u6240\u5217\u4E4B\u552E\u50F9\u70BA\u4E0A\u9650\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "9.\u7532\u65B9\u61C9\u5584\u76E1\u5C0D\u65E5\u5E38\u4FDD\u990A\u53CA\u7DAD\u8B77\u4E4B\u8CAC\u4EFB\uFF0C\u6B78\u9084\u66AB\u501F\u8A2D\u5099\u6642\uFF0C\u9808\u7D93\u4E59\u65B9\u78BA\u8A8D\u66AB\u501F\u8A2D\u5099\u5916\u89C0\u8207\u529F\u80FD\u7121\u8AA4\uFF0C\u4E26\u61C9\u7531\u96D9\u65B9\u7C3D\u540D\u78BA\u8A8D\u3002\u5982\u672A\u7D93\u96D9\u65B9\u7C3D\u7F72\u78BA\u8A8D\uFF0C" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 9 }) }, { children: "\u8996\u70BA\u5C1A\u672A\u5B8C\u6210\u6B78\u9084\u3002" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.terms }, { children: "10.\u672C\u66AB\u501F\u5408\u7D04\u66F8\u56E0\u5C65\u884C\u6240\u751F\u4E4B\u4E00\u5207\u722D\u8B70\uFF0C\u96D9\u65B9\u540C\u610F\u4EE5\u4E2D\u83EF\u6C11\u570B\u6CD5\u5F8B\u70BA\u6E96\u64DA\u6CD5\uFF0C\u4E26\u4EE5\u4E59\u65B9\u501F\u51FA\u9580\u5E02\u6240\u5728\u5730\u5730\u65B9\u6CD5\u9662\u70BA\u7B2C\u4E00\u5BE9\u7BA1\u8F44\u6CD5\u9662\u3002\u60DF\u7532\u65B9\u70BA\u6D88\u8CBB" })) }), (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: __assign(__assign({}, styles.terms), { marginLeft: 12 }) }, { children: "\u8005\u8005\uFF0C\u4ECD\u5F97\u4F9D\u6D88\u8CBB\u8005\u4FDD\u8B77\u6CD5\u7B2C47\u689D\u898F\u5B9A\u9032\u884C\u3002" })) }), (0, jsx_runtime_1.jsxs)(table_1.Table, __assign({ style: { marginTop: 10, marginBottom: 6 } }, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u66AB\u501F\u8005(\u7532\u65B9):" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u501F\u51FA\u8005(\u4E59\u65B9):" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '12%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u9580\u5E02:" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '14%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })) }))] }), (0, jsx_runtime_1.jsx)(table_1.TableRow, { children: (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '100%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u66AB\u501F\u8005(\u7532\u65B9): ____________________ \u5DF2\u6B78\u9084\u4E0A\u8FF0\u5404\u9805\u8CA8\u54C1\uFF0C\u4E26\u5DF2\u6536\u5230\u4E0A\u8FF0\u5168\u6578\u62BC\u91D1\u91D1\u984D\u53CA\u8EAB\u5206\u8B49\u6B63\u53CD\u9762\u5F71\u672C" }) })) })) }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6536\u8CA8\u8005:" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u9580\u5E02:" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '12%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u65E5\u671F:" }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '14%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { height: '5mm', justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })) }))] })] })), !hiddenFooter && ((0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { margin: '8px 0' } }, { children: (0, jsx_runtime_1.jsx)(ISOFooter_1.default, { date: (_f = booking === null || booking === void 0 ? void 0 : booking.docInfo.date) !== null && _f !== void 0 ? _f : '', version: (_g = booking === null || booking === void 0 ? void 0 : booking.docInfo.version) !== null && _g !== void 0 ? _g : '', fileCode: (_h = booking === null || booking === void 0 ? void 0 : booking.docInfo.fileCode) !== null && _h !== void 0 ? _h : '', lang: lang }) })))] })));
};
exports.default = BookingPage;
