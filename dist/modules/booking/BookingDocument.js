"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var BookingPage_1 = __importDefault(require("./BookingPage"));
var BookingPageCN_1 = __importDefault(require("./BookingPageCN"));
var consts_1 = require("../../consts");
var BookingDocument = function (props) {
    var companyTitle = props.companyTitle, booking = props.booking, hiddenFooter = props.hiddenFooter, _a = props.lang, lang = _a === void 0 ? consts_1.EnumLanguage['zh-tw'] : _a;
    return ((0, jsx_runtime_1.jsxs)(renderer_1.Document, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, { children: [(0, jsx_runtime_1.jsx)(BookingPageCN_1.default, { booking: booking, hiddenFooter: hiddenFooter, lang: lang }), (0, jsx_runtime_1.jsx)(BookingPageCN_1.default, { booking: booking, hiddenFooter: hiddenFooter, lang: lang })] })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsxs)(jsx_runtime_1.Fragment, { children: [(0, jsx_runtime_1.jsx)(BookingPage_1.default, { companyTitle: companyTitle, booking: booking, hiddenFooter: hiddenFooter, lang: lang }), (0, jsx_runtime_1.jsx)(BookingPage_1.default, { companyTitle: companyTitle, booking: booking, hiddenFooter: hiddenFooter, lang: lang })] }))] }));
};
exports.default = BookingDocument;
