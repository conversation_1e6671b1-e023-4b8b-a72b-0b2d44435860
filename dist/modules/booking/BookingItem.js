"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var numeral_1 = __importDefault(require("numeral"));
var renderer_1 = require("@react-pdf/renderer");
var table_1 = require("../../components/table");
var styles = renderer_1.StyleSheet.create({
    componentRow: {
        display: 'flex',
        flexDirection: 'row',
    },
    componentCol: {
        padding: '0px 2px',
    },
    underLine: {
        borderTop: '1px solid black',
    },
});
var RentDeviceTable = function (props) {
    var rentDevice = props.rentDevice;
    return ((0, jsx_runtime_1.jsxs)(table_1.TableRow, __assign({ style: { height: '14mm' } }, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: rentDevice && rentDevice.type }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '40%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: rentDevice && rentDevice.name }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: rentDevice && rentDevice.SN }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '10%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: rentDevice && '1' }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '15%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { justifyContent: 'center' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: rentDevice && Number(rentDevice.price) > 0 && (0, numeral_1.default)(rentDevice.price).format('0,0') }) })) }))] })));
};
exports.default = RentDeviceTable;
