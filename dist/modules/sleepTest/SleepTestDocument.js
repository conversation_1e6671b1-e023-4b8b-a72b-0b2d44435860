"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var SleepTestPage_1 = __importDefault(require("./SleepTestPage"));
var SleepTestPageCN_1 = __importDefault(require("./SleepTestPageCN"));
var consts_1 = require("../../consts");
var SleepTestDocument = function (props) {
    var sleepTest = props.sleepTest, hiddenFooter = props.hiddenFooter, _a = props.lang, lang = _a === void 0 ? consts_1.EnumLanguage['zh-tw'] : _a;
    return ((0, jsx_runtime_1.jsxs)(renderer_1.Document, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(SleepTestPageCN_1.default, { sleepTest: sleepTest, hiddenFooter: hiddenFooter, lang: lang }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(SleepTestPage_1.default, { sleepTest: sleepTest, hiddenFooter: hiddenFooter, lang: lang }) }))] }));
};
exports.default = SleepTestDocument;
