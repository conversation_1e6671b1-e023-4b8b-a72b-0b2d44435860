"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.PulseTableRowsCN = exports.BloodTableRowsCN = exports.AHITableRowsCN = exports.PulseRangeContent = exports.PulseUnitText = exports.PulseBottomBorder = exports.PulseTableRows = exports.BloodRangeContent = exports.BloodUnitText = exports.BloodBottomBorder = exports.BloodTableRows = exports.AHIRangeContent = exports.AHIBottomBorder = exports.AHIUnitText = exports.AHITableRows = void 0;
exports.AHITableRows = [
    '呼吸中止指數(AHI)',
    '阻塞指數(Apnea index)',
    '無法判定,不清楚(UAI)',
    '阻塞型中止指數(OAI)',
    '中樞型中止指數(CAI)',
    '混合型中止指數(MAI)',
    '低通氣指數(hyponea Index)',
    '打鼾頻率(Snore)',
];
exports.AHIUnitText = [null, null, null, '次/h', null, null];
exports.AHIBottomBorder = ['none', 'none', 'none', 'none', 'none', 'none', null, null];
exports.AHIRangeContent = ['<5/h', '<5/h', '', '', '', '', '<5/h', ''];
exports.BloodTableRows = ['ODI 血氧低下指標', '平均血氧濃度', '最低血氧濃度', '基線血氧濃度'];
exports.BloodBottomBorder = [null, null, null, null];
exports.BloodUnitText = ['次/h', '%', '%', '%'];
exports.BloodRangeContent = ['<5/h', '94% ~ 98%', '90% ~ 98%', '%'];
exports.PulseTableRows = ['最小脈搏', '最大脈搏', '平均脈搏'];
exports.PulseBottomBorder = [null, null, null, null];
exports.PulseUnitText = ['bpm', 'bmp', 'bmp'];
exports.PulseRangeContent = ['50 ~ 70 ', '60 ~ 90', ''];
// China
exports.AHITableRowsCN = [
    '呼吸中止指数(AHI)',
    '阻塞指数(Apnea index)',
    '无法判定,不清楚(UAI)',
    '阻塞型中止指数(OAI)',
    '中枢型中止指数(CAI)',
    '混合型中止指数(MAI)',
    '低通气指数(hyponea Index)',
    '打鼾频率(Snore)',
];
exports.BloodTableRowsCN = ['ODI 血氧低下指标', '平均血氧浓度', '最低血氧浓度', '基线血氧浓度'];
exports.PulseTableRowsCN = ['最小脉搏', '最大脉搏', '平均脉搏'];
