"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AhiContentTable = exports.ContentHeader = void 0;
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var consts_1 = require("../../consts");
var table_1 = require("../../components/table");
var ContentHeader = function (props) {
    var lang = props.lang;
    return ((0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.Table<PERSON>ell, __assign({ style: { width: '40%' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u540D\u79F0" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u540D\u7A31" }) }))] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20%', textAlign: 'right', borderRight: 'none' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6307\u6570" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6307\u6578" }) }))] }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '10%', borderLeft: 'none' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, {}) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '30%' } }, { children: (0, jsx_runtime_1.jsxs)(renderer_1.View, { children: [lang === consts_1.EnumLanguage['zh-cn'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6B63\u5E38\u8303\u56F4" }) })), lang === consts_1.EnumLanguage['zh-tw'] && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6B63\u5E38\u7BC4\u570D" }) }))] }) }))] }));
};
exports.ContentHeader = ContentHeader;
var AhiContentTable = function (props) {
    var contentTitle = props.contentTitle, bottomBorder = props.bottomBorder, rangeContent = props.rangeContent, unitText = props.unitText, data = props.data;
    return ((0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '40%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: contentTitle }) }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '20%', textAlign: 'right', borderRight: '' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: data }) }) })), bottomBorder && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '10%', borderBottom: 'none', backgroundColor: '#D3D3D3' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: unitText }) })) })), !bottomBorder && ((0, jsx_runtime_1.jsx)(jsx_runtime_1.Fragment, { children: (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '10%', backgroundColor: '#D3D3D3' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: unitText }) })) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '30%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: rangeContent }) }) }))] }));
};
exports.AhiContentTable = AhiContentTable;
