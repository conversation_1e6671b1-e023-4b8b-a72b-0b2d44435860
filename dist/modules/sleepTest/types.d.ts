export interface SleepTestData {
    id?: number;
    breathe?: {
        ahi: number;
        apnea: number;
        uai: number;
        oai: number;
        cai: number;
        mai: number;
        hyponea: number;
        snore: number;
    };
    bloodOxygen?: {
        odi: number;
        avgBloodOxygen: number;
        minBloodOxygen: number;
        baseBloodOxygen: number;
    };
    pulse?: {
        minPulse: number;
        maxPulse: number;
        avgPulse: number;
    };
    member: ISleepTestMember;
}
export interface ISleepTestMember {
    name: string;
    bmi?: number;
    birthday?: string;
    sex?: string;
}
