"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var table_1 = require("../../components/table");
var consts_1 = require("../../consts");
var ISOFooter_1 = __importDefault(require("../common/ISOFooter"));
var CheckBox_1 = __importDefault(require("../common/CheckBox"));
var ahiContent_1 = require("./ahiContent");
var consts_2 = require("./consts");
var path_1 = __importDefault(require("path"));
renderer_1.Font.register({
    family: consts_1.TRADITIONAL_CHINESE,
    fonts: [
        {
            src: path_1.default.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'bold',
        },
        {
            src: path_1.default.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'medium',
        },
        {
            src: path_1.default.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'regular',
        },
    ],
});
renderer_1.Font.registerHyphenationCallback(function (word) { return word.split(''); });
var styles = renderer_1.StyleSheet.create({
    page: {
        fontFamily: consts_1.TRADITIONAL_CHINESE,
        backgroundColor: 'white',
        fontSize: 10,
        padding: '8mm 10mm',
    },
    row: {
        display: 'flex',
        flexDirection: 'row',
    },
    title: {
        fontSize: 13,
        fontWeight: 'bold',
    },
    terms: {
        fontSize: 9,
        lineHeight: '1.5',
    },
});
// ISO docInfo
var docInfo = {
    date: '2023-02-08',
    version: '1',
    fileCode: 'SC-N-4-7-161',
};
var SleepTestPage = function (props) {
    var _a, _b, _c;
    var sleepTest = props.sleepTest, _d = props.hiddenFooter, hiddenFooter = _d === void 0 ? false : _d, lang = props.lang;
    var breatheData = [];
    var bloodOxygenData = [];
    var pulseData = [];
    if (sleepTest === null || sleepTest === void 0 ? void 0 : sleepTest.breathe) {
        breatheData = Object.values(sleepTest === null || sleepTest === void 0 ? void 0 : sleepTest.breathe);
    }
    if (sleepTest === null || sleepTest === void 0 ? void 0 : sleepTest.bloodOxygen) {
        bloodOxygenData = Object.values(sleepTest.bloodOxygen);
    }
    if (sleepTest === null || sleepTest === void 0 ? void 0 : sleepTest.pulse) {
        pulseData = Object.values(sleepTest === null || sleepTest === void 0 ? void 0 : sleepTest.pulse);
    }
    return ((0, jsx_runtime_1.jsxs)(renderer_1.Page, __assign({ size: "A4", style: styles.page }, { children: [(0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ fixed: true }, { children: (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { textAlign: 'center', marginBottom: '2mm' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: { fontSize: '24pt', lineHeight: '1.5', fontWeight: 'bold' } }, { children: "ApneaLink Air \u7761\u7720\u6AA2\u6E2C\u5831\u544A" })) })) })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { marginTop: 17, marginBottom: 4 }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.title }, { children: "\u5BA2\u6236\u8CC7\u6599" })) })), (0, jsx_runtime_1.jsxs)(table_1.Table, { children: [(0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u59D3\u540D:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '18.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_a = sleepTest === null || sleepTest === void 0 ? void 0 : sleepTest.member) === null || _a === void 0 ? void 0 : _a.name }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6D41\u6C34\u865F:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '18.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u51FA\u751F\u5E74\u6708\u65E5:" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '18.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_b = sleepTest === null || sleepTest === void 0 ? void 0 : sleepTest.member) === null || _b === void 0 ? void 0 : _b.birthday }) }))] }), (0, jsx_runtime_1.jsxs)(table_1.TableRow, { children: [(0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "\u6027\u5225:" }) })), (0, jsx_runtime_1.jsxs)(table_1.TableCell, __assign({ style: { width: '18.4%' } }, { children: [(0, jsx_runtime_1.jsx)(CheckBox_1.default, { style: { marginRight: 4, marginLeft: 6 }, check: (sleepTest === null || sleepTest === void 0 ? void 0 : sleepTest.member.sex) == '0', fontSize: '10pt', labelFontSize: '9pt', label: " \u5973" }), (0, jsx_runtime_1.jsx)(CheckBox_1.default, { style: { marginRight: 4, marginLeft: 40, marginTop: -10.7 }, check: (sleepTest === null || sleepTest === void 0 ? void 0 : sleepTest.member.sex) == '1', fontSize: '10pt', labelFontSize: '9pt', label: " \u7537" })] })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: "BMI :" }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '18.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { children: (_c = sleepTest === null || sleepTest === void 0 ? void 0 : sleepTest.member) === null || _c === void 0 ? void 0 : _c.bmi }) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '16.6%', borderRight: 'none' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) })), (0, jsx_runtime_1.jsx)(table_1.TableCell, __assign({ style: { width: '18.4%' } }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, {}) }))] })] }), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { marginTop: 17, marginBottom: 4 }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, __assign({ style: styles.title }, { children: "\u6AA2\u6E2C\u7D50\u679C" })) })), (0, jsx_runtime_1.jsxs)(table_1.Table, __assign({ wrap: false }, { children: [(0, jsx_runtime_1.jsx)(ahiContent_1.ContentHeader, { lang: lang }), consts_2.AHITableRows.map(function (item, index) { return ((0, jsx_runtime_1.jsx)(ahiContent_1.AhiContentTable, { data: breatheData[index], contentTitle: item, bottomBorder: consts_2.AHIBottomBorder[index], rangeContent: consts_2.AHIRangeContent[index], unitText: consts_2.AHIUnitText[index], lang: lang }, index)); })] })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { marginTop: 17, marginBottom: 6 }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { style: styles.title }) })), (0, jsx_runtime_1.jsxs)(table_1.Table, __assign({ wrap: false }, { children: [(0, jsx_runtime_1.jsx)(ahiContent_1.ContentHeader, { lang: lang }), consts_2.BloodTableRows.map(function (item, index) { return ((0, jsx_runtime_1.jsx)(ahiContent_1.AhiContentTable, { data: bloodOxygenData[index], contentTitle: item, bottomBorder: consts_2.BloodBottomBorder[index], rangeContent: consts_2.BloodRangeContent[index], unitText: consts_2.BloodUnitText[index], lang: lang }, index)); })] })), (0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: __assign(__assign({}, styles.row), { marginTop: 17, marginBottom: 6 }) }, { children: (0, jsx_runtime_1.jsx)(renderer_1.Text, { style: styles.title }) })), (0, jsx_runtime_1.jsxs)(table_1.Table, __assign({ wrap: false }, { children: [(0, jsx_runtime_1.jsx)(ahiContent_1.ContentHeader, { lang: lang }), consts_2.PulseTableRows.map(function (item, index) { return ((0, jsx_runtime_1.jsx)(ahiContent_1.AhiContentTable, { data: pulseData[index], contentTitle: item, bottomBorder: consts_2.PulseBottomBorder[index], rangeContent: consts_2.PulseRangeContent[index], unitText: consts_2.PulseUnitText[index], lang: lang }, index)); })] })), !hiddenFooter && ((0, jsx_runtime_1.jsx)(renderer_1.View, __assign({ style: { marginTop: '42px' } }, { children: (0, jsx_runtime_1.jsx)(ISOFooter_1.default, { date: docInfo.date, version: docInfo.version, fileCode: docInfo.fileCode, lang: lang }) })))] })));
};
exports.default = SleepTestPage;
