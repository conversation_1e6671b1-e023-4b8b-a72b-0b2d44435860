"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PDFHelper = void 0;
var renderer_1 = require("@react-pdf/renderer");
var react_1 = __importDefault(require("react"));
var PDFHelper = /** @class */ (function () {
    function PDFHelper(component, props) {
        this.component = react_1.default.createElement(component, props);
    }
    PDFHelper.prototype.toBase64 = function () {
        return (0, renderer_1.renderToString)(this.component);
    };
    PDFHelper.prototype.toFile = function (filePath) {
        return (0, renderer_1.renderToFile)(this.component, filePath);
    };
    PDFHelper.prototype.toStream = function () {
        return (0, renderer_1.renderToStream)(this.component);
    };
    return PDFHelper;
}());
exports.PDFHelper = PDFHelper;
