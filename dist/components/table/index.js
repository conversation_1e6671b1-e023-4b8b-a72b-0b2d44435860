"use strict";
var __assign = (this && this.__assign) || function () {
    __assign = Object.assign || function(t) {
        for (var s, i = 1, n = arguments.length; i < n; i++) {
            s = arguments[i];
            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))
                t[p] = s[p];
        }
        return t;
    };
    return __assign.apply(this, arguments);
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TableCell = exports.TableRow = exports.Table = void 0;
var jsx_runtime_1 = require("react/jsx-runtime");
var renderer_1 = require("@react-pdf/renderer");
var Table = function (props) {
    return ((0, jsx_runtime_1.jsx)(renderer_1.View, __assign({}, props, { style: __assign({ borderStyle: 'solid', borderColor: 'black', borderLeft: '1px', borderTop: '1px' }, props.style) }, { children: props.children })));
};
exports.Table = Table;
var TableRow = function (props) {
    return ((0, jsx_runtime_1.jsx)(renderer_1.View, __assign({}, props, { style: __assign({ display: 'flex', flexDirection: 'row' }, props.style) }, { children: props.children })));
};
exports.TableRow = TableRow;
var TableCell = function (_a) {
    var _b = _a.dense, dense = _b === void 0 ? false : _b, props = __rest(_a, ["dense"]);
    return ((0, jsx_runtime_1.jsx)(renderer_1.View, __assign({}, props, { style: __assign({ padding: dense ? '4px 4px' : '8px 8px', justifyContent: 'center', borderStyle: 'solid', borderColor: 'black', borderRight: '1px', borderBottom: '1px' }, props.style) }, { children: props.children })));
};
exports.TableCell = TableCell;
