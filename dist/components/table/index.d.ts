import { View } from '@react-pdf/renderer';
import React from 'react';
interface TableProps extends React.ComponentProps<typeof View> {
}
export declare const Table: React.FC<TableProps>;
interface TableRowProps extends React.ComponentProps<typeof View> {
}
export declare const TableRow: React.FC<TableRowProps>;
interface TableCellProps extends React.ComponentProps<typeof View> {
    dense?: boolean;
}
export declare const TableCell: React.FC<TableCellProps>;
export {};
