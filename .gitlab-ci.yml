stages:
  # - test
  - build
  - deploy

default:
  image: ***********:5000/node-with-oracle-client:18.13.0-alpine3.17-canvas

# test:
#   stage: test
#   only:
#     - dev
#     - master
#     - tags
#   script:
#     - npm i
#     - npx jest

build:
  stage: build
  only:
    - dev
    - master
    - tags
  cache:
    key: $CI_COMMIT_REF_SLUG
    paths:
      - node_modules/
    untracked: true
    policy: push
  artifacts:
    paths:
      - dist/
    untracked: true
    expire_in: 1 day
  script:
    - npm i
    - npm run build

deploy:
  stage: deploy
  only:
    - tags
  cache:
    key: $CI_COMMIT_REF_SLUG
    paths:
      - node_modules/
    untracked: true
    policy: pull
  dependencies:
    - build
  script:
    - rm .npmrc
    - echo "registry=http://***********:8081/repository/npm/" >> .npmrc
    - echo "//***********:8081/repository/npm-hosted/:_authToken=$NPM_AUTH_TOKEN" >> .npmrc
    - npm publish
  
