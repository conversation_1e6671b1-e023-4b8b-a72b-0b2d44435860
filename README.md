# PDF 產生工具
產生PDF的工具，單純放template。

* 基本上是一個 React 專案
* 匯出package只包含 `src/lib` (放入src方便用react 開發)
* 圖片先轉base64使用，兼容nodejs, web(待研究),專案不好轉出一般圖片檔(todo)。
* `src`除了`lib`都是開發中用或是給`storybook`用。
* 要用中文必須先載入中文字型
* 中文必須改良斷句
``` typescript
Font.registerHyphenationCallback((word) => word.split(''));
```

## 開發
主要內容在 `src/lib`

### React開發
用Storybook開發好像比較適合。
``` sh
$ npm run storybook
```

## 使用
### Node.js
``` ts
import path from 'path';

import { Font } from '@react-pdf/renderer';
import { EarModelOrderDocument, PDFHelper, TRADITIONAL_CHINESE } from '@clinico/pdf-template';

Font.register({ family: TRADITIONAL_CHINESE, src: path.join(__dirname, '../public/PMingLiU.ttf') });
Font.registerHyphenationCallback((word) => word.split(''));

(async () => {
    const helper = new PDFHelper(EarModelOrderDocument, {
        // props
    });
    return await helper.toFile('./example.pdf');
})();

```

## storybook 字體設定
當要透過storybook需要調整字體的路徑, 避免看到中文亂碼的PDF。
``` ts
Font.register({ 
    family: TRADITIONAL_CHINESE, 
    fonts: [
        {
            src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'bold',
        },
        {
            src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'medium',
        },
        {
            src: path.join(__dirname, '../../assets/fonts/SourceHanSansHWTC-VF.ttf'),
            fontWeight: 'regular',
        },
    ]
});
```

## storybook 字體設定
當要透過storybook查看PDF需要調整字體的路徑, 避免看到中文亂碼的PDF。

只需要保留字體名稱,如有新增字體檔案存放路徑為  `/src/lib/assets/fonts/`

__原始__
``` ts
Font.register({ family: TRADITIONAL_CHINESE, src: path.join(__dirname, '../public/PMingLiU.ttf') });
```
__storybook__
``` ts
Font.register({ family: TRADITIONAL_CHINESE, src: 'PMingLiU.ttf' });
```
